using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace OutpOracleDAL
{
    public class OrderStandardDal :OrdersBase, IOrder
    {
        public void AddDeleteIndOutpOrderCosts(ref Dictionary<string, string> idc, string HisUnitCode, string ClinicNo, string OrderNo, string OrderSubNo, string ItemNo)
        {
            string sqlc = " delete from  ind_OUTP_ORDERS_COSTS  where his_unit_code = '" + HisUnitCode + "'and  CLINIC_NO = '" + ClinicNo + "' and ORDER_NO = " + OrderNo + " and ORDER_SUB_NO = " + OrderSubNo + " and ITEM_NO = " + ItemNo;
            idc.Add(sqlc, "删除outp_orders_costs_STANDARD项目失败");
        }

        public void AddDeleteIndOutpOrderCosts(ref Dictionary<string, string> idc, string hisUnitCode, string clinicNo, string orderNo, string orderSubNo)
        {
            string sql = " delete from  ind_OUTP_ORDERS_COSTS  where his_unit_code = '" + hisUnitCode + "'and  CLINIC_NO = '" + clinicNo + "' and ORDER_NO = " + orderNo + " and ORDER_SUB_NO = " + orderSubNo;
            idc.Add(sql, "删除ind_outp_orders_costs项目失败");
        }

        public string GetSumChargeIndicatorOrderCosts()
        {
           return "select nvl(sum(CHARGE_INDICATOR),0) from OUTPDOCT.OUTP_ORDERS_COSTS_STANDARD where his_unit_code =:t1 and  CLINIC_NO = :t2 and ORDER_NO = :t3";
        }

        public string GetCountOrders(string ClinicNo, string OrderNo, string OrderSubNo, string ItemNo)
        {
            return GetCountOrders(ClinicNo, OrderNo, OrderSubNo) + " AND ITEM_NO = " + ItemNo;
        }

        public string GetCountOrders(string ClinicNo, string OrderNo, string OrderSubNo)
        {
            return "select count(*) from OUTPDOCT.OUTP_ORDERS_STANDARD where CLINIC_NO = '" + ClinicNo + "' and ORDER_NO = " + OrderNo + " and ORDER_SUB_NO = " + OrderSubNo; 
        }

        public string GetDeleteOrdersWithItem(string HisUnitCode, string ClinicNo, string OrderNo, string OrderSubNo, string ItemNo)
        {
            return "";
        }

        public void GetDeleteOrders(ref Dictionary<string, string> idc, string HisUnitCode, string ClinicNo, string OrderNo, string OrderSubNo,string OrderClass="")
        {
            string sqlc = "delete from  outp_orders_STANDARD  where his_unit_code = '" + HisUnitCode + "'and  CLINIC_NO = '" + ClinicNo + "' and ORDER_NO = " + OrderNo + " and ORDER_SUB_NO = " + OrderSubNo;
            idc.Add(sqlc, "删除outp_orders_STANDARD项目失败");
        }

        public void GetDeleteOrdersCosts(ref Dictionary<string, string> idc, string HisUnitCode, string ClinicNo, string OrderNo, string OrderSubNo,string ItemNo)
        {
            string sqlc= " delete from  OUTP_ORDERS_COSTS_STANDARD  where his_unit_code = '" + HisUnitCode + "'and  CLINIC_NO = '" + ClinicNo + "' and ORDER_NO = " + OrderNo + " and ORDER_SUB_NO = " + OrderSubNo + " and ITEM_NO = " + ItemNo;
            idc.Add(sqlc, "删除outp_orders_costs_STANDARD项目失败");
        }

        public void GetDeleteOrdersCosts(ref Dictionary<string, string> idc, string HisUnitCode, string ClinicNo, string OrderNo, string OrderSubNo)
        {
            string sqlc=" delete from  OUTP_ORDERS_COSTS_STANDARD  where his_unit_code = '" + HisUnitCode + "'and  CLINIC_NO = '" + ClinicNo + "' and ORDER_NO = " + OrderNo + " and ORDER_SUB_NO = " + OrderSubNo;
            idc.Add(sqlc, "删除outp_orders_costs_STANDARD项目失败");
        }

        public int GetMaxItemNo(string clinicNo)
        {
            return 0;
        }

        /// <summary>
        /// 两个参数的，带hisUnitCode
        /// </summary>
        /// <returns></returns>
        public string GetOrderNo()
        {
            return "select nvl(max(order_no),0)  from OUTP_ORDERS_STANDARD where his_unit_code = :t1 and clinic_no = :t2";
        }

        public string GetOrdersCostsCount(string HisUnitCode, string ClinicNo, string OrderNo, string OrderSubNo, string ItemNo)
        {
            return "select count(*) from OUTPDOCT.OUTP_ORDERS_COSTS_STANDARD where his_unit_code = '" + HisUnitCode + "'and  CLINIC_NO = '" + ClinicNo + "' and ORDER_NO = " + OrderNo + " and ORDER_SUB_NO = " + OrderSubNo + " and ITEM_NO = " + ItemNo;
        }

        public string GetOrdersTreatItemCount(string ClinicNo, string OrderNo, string OrderSubNo)
        {
            throw new NotImplementedException();
        }

        public string GetSelectOrderCosts()
        {
            throw new NotImplementedException();
        }

        public string GetSelectOrderCosts(string ClinicNo)
        {
            StringBuilder sqlb = new StringBuilder();
            sqlb.Append(" SELECT  PATIENT_ID ,CLINIC_NO  , ORDER_CLASS , ORDER_NO , ORDER_SUB_NO,'' as gj_code,'' as gj_name,'' as GJBM,'' as GJMC,'' as SJBM,'' as SJMC,RECIPETYPE,");//军队医改2022
            sqlb.Append(" ITEM_NO , ITEM_CLASS, ITEM_NAME , ITEM_CODE , ITEM_SPEC , UNITS, REPETITION , AMOUNT,");
            sqlb.Append(" CLASS_ON_RCPT, COSTS , CHARGES ,RCPT_NO ,CHARGE_INDICATOR ,  CLASS_ON_RECKONING ,SUBJ_CODE ,");
            sqlb.Append(" PRICE_QUOTIETY ,ITEM_PRICE  , BILL_DATE , BILL_NO , INSURANCE_FLAG , OPER_DATE, OPER_ID ,");
            sqlb.Append("  CLXZBS  ,CLWZM ,HIS_UNIT_CODE, OUTP_SERIAL_NO ,PERFORMED_BY,YPXZBS,INSUR_ADULT  ");
            sqlb.Append("  , TRADE_PRICE	,BATCH_CODE	,BATCH_NO	,GUID	 "); //20220402
            sqlb.Append(" FROM OUTPDOCT.OUTP_ORDERS_COSTS_STANDARD   ");
            sqlb.Append(" WHERE CLINIC_NO = '" + ClinicNo + "' ORDER BY  ORDER_NO,ORDER_SUB_NO asc ");
            return sqlb.ToString();
        }

        public string GetSelectOrderExist()
        {
            return " select clinic_no,order_no,ORDER_SUB_NO,DOCTOR_NO from OUTPDOCT.OUTP_ORDERS_STANDARD  where APPOINT_NO = :t1 and patient_id = :patient_id";
        }

        public string GetSelectOrders()
        {
            throw new NotImplementedException();
        }

        public string GetSelectOrders(string ClinicNo)
        {
            StringBuilder sqla = new StringBuilder();
            //RECIPETYPE 军队医改2022
            sqla.Append(" SELECT  PATIENT_ID   , CLINIC_NO     , ORDER_NO    ,ORDER_SUB_NO  ,  REPETITION,'' as gj_code,'' as gj_name,'' as GJBM,'' as GJMC,'' as SJBM,'' as SJMC,'' as INSUR_ADULT, ");
            sqla.Append(" ORDERED_BY  ,DOCTOR   ,ORDER_DATE  ,DOCTOR_NO , NURSE  , SIGNATURE_NO , USAGE_DESC  , DIAGNOSIS_DESC ,  ");
            sqla.Append(" HIS_UNIT_CODE ,OUTP_SERIAL_NO ,RCPT_NO , APPOINT_NO , ORDER_CLASS, ORDER_TEXT , ORDER_CODE ,DOSAGE ,DOSAGE_UNITS , ");
            sqla.Append("  ADMINISTRATION , FREQUENCY ,SKINTEST,PRESC_PSNO  , AMOUNT   , PERFORMED_BY  ,COSTS   ,CHARGES   ,batch_no,batch_code, ");
            sqla.Append(" decode(PERFORMED_BY,null,'', (select dept_name from dept_dict where dept_code = OUTPDOCT.OUTP_ORDERS_STANDARD.PERFORMED_BY )) PERFORMED_BY_NAME, ");
            sqla.Append("  SKIN_SAVE     ,SKIN_START   ,SKIN_BATH     ,SKIN_OPER_NO ,SKIN_FLAG,CHARGE_INDICATOR,to_char(SPLIT_FLAG) SPLIT_FLAG,ITEM_SPEC,FIRM_ID,PRESC_ATTR ,  ");
            sqla.Append(" UNITS,ABIDANCE,PERFORM_TIMES,FREQ_DETAIL,'' as INPUT_NAME,'0' del_indicator,  '普通药品' toxi_property,1 drug_indicator,'' official_catalog,    ");
            sqla.Append("  0.0 dosc_per_unit,   0.0 amount_per_package,   0 freq_counter,    0 freq_interval,    '' freq_interval_unit,   0 as virtual_prescno, 0 as nwarn , nvl(DECOCTION,'0') as DECOCTION,COUNT_PER_REPETITION ,0 order_drug_count,0 order_no_sort,'' insurance_memo, ");
            sqla.Append(" 0 usable_quantity,'' min_spec,'' dup_presc_no,'0' OLD_AMOUNT ,'' sub_flag,SKIN_RESULT,BATCH_NO,DOSE_PER_UNIT,(select OUTPDOCT.OUTP_ORDERS_COSTS_STANDARD.YPXZBS FROM OUTPDOCT.OUTP_ORDERS_COSTS_STANDARD WHERE OUTPDOCT.OUTP_ORDERS_COSTS_STANDARD.CLINIC_NO=OUTPDOCT.OUTP_ORDERS_STANDARD.CLINIC_NO AND OUTPDOCT.OUTP_ORDERS_COSTS_STANDARD.ORDER_NO=OUTPDOCT.OUTP_ORDERS_STANDARD.ORDER_NO AND OUTPDOCT.OUTP_ORDERS_COSTS_STANDARD.ORDER_SUB_NO = OUTPDOCT.OUTP_ORDERS_STANDARD.ORDER_SUB_NO AND (OUTPDOCT.OUTP_ORDERS_COSTS_STANDARD.ITEM_CLASS='A' OR OUTPDOCT.OUTP_ORDERS_COSTS_STANDARD.ITEM_CLASS='B' ) AND ROWNUM=1) YPXZBS,(select OUTPDOCT.OUTP_ORDERS_COSTS_STANDARD.INSUR_ADULT FROM OUTPDOCT.OUTP_ORDERS_COSTS_STANDARD WHERE OUTPDOCT.OUTP_ORDERS_COSTS_STANDARD.CLINIC_NO=OUTPDOCT.OUTP_ORDERS_STANDARD.CLINIC_NO AND OUTPDOCT.OUTP_ORDERS_COSTS_STANDARD.ORDER_NO=OUTPDOCT.OUTP_ORDERS_STANDARD.ORDER_NO AND OUTPDOCT.OUTP_ORDERS_COSTS_STANDARD.ORDER_SUB_NO = OUTPDOCT.OUTP_ORDERS_STANDARD.ORDER_SUB_NO AND (OUTPDOCT.OUTP_ORDERS_COSTS_STANDARD.ITEM_CLASS='A' OR OUTPDOCT.OUTP_ORDERS_COSTS_STANDARD.ITEM_CLASS='B' ) AND ROWNUM=1) INSUR_ADULT,DECODE((SELECT D.HIGH_DANGER FROM DRUG_DICT D WHERE D.DRUG_CODE=OUTPDOCT.OUTP_ORDERS_STANDARD.ORDER_CODE and d.dose_per_unit=OUTPDOCT.OUTP_ORDERS_STANDARD.dose_per_unit and rownum=1) ,'1','高','') HIGH_DANGER ");
            sqla.Append(" ,TREAT_ITEM   ");
            sqla.Append("  , TRADE_PRICE ,BATCH_CODE	,GUID	,ITEM_PRICE ,RECIPETYPE "); //20220402 按照医嘱序号排序
            sqla.Append(" ,PRINT_STATUS");//lilei 20221124增加打印状态
            sqla.Append(" FROM OUTPDOCT.OUTP_ORDERS_STANDARD  ");
            sqla.Append(" WHERE CLINIC_NO = '" + ClinicNo + "' ORDER BY  ORDER_NO,ORDER_SUB_NO asc ");
            return sqla.ToString();
        }

        public string GetSumOrderCosts(string hisUnitCode, string clinicNo, string orderNo, string orderSubNo)
        {
            return "select nvl(sum(CHARGE_INDICATOR),0) from OUTPDOCT.OUTP_ORDERS_COSTS_STANDARD where his_unit_code = '" + hisUnitCode + "'and  CLINIC_NO = '" + clinicNo + "' and ORDER_NO = " + orderNo + " and ORDER_SUB_NO = " + orderSubNo;
        }

        public int SetOrder(ref Dictionary<string, string> idc, string patientId, string serialNo, string orderedBy, string doctor, string clinicNo, string doctorNo, string orderDate, string hisUnitCode)
        {
            return 0;
        }

        public int set_outporders(ref Dictionary<string, string> idc, string PatientId, string ClinicNo, string OrderNo, string OrderSubNo, string OrderedBy, string DoctorName, string OrderDate, string DoctorNo, string Nurse, string Signatureno, string Usagedesc, string DiagnosisDesc, string HisUnitCode, string OutpSerialNo, string AppointNo, string OrderClass, string OrderText, string OrderCode, string Dosage, string DosageUnits, string Administration, string Frequency, string Skintest, string Prescpsno, decimal Amount, string Performedby, decimal Costs, decimal Charges, string SplitFlag, string ItemSpec, string FirmId, string Repetition, string PrescAttr, string Units, string Abidance, string PerformTimes, string FreqDetail, string BatchNo, string DosePerUnit, decimal _TRADE_PRICE, string _BATCH_CODE, string _GUID, decimal _ITEM_PRICE, int itemNo, string bjca_sn = "", string bjca_value = "", string bjca_time = "", string Decoction = "", string COUNT_PER_REPETITION = "null", string RECIPETYPE = "")
        {
            if (Amount == 0)
            {
                Amount = 1;
            }

            //string sqlc = "select  VISIT_DATE,VISIT_NO from clinic_master where clinic_no = :t1";
            //System.Collections.ArrayList list1 = new System.Collections.ArrayList();
            //List<string> paras1 = new List<string>();
            //paras1.Add("t1");
            //list1.Add(ClinicNo);
            //DataTable dtclinic = new ServerPublic_Dao().GetDataTable_Para(sqlc, paras1, list1).Tables[0];
            //if (dtclinic.Rows.Count <= 0)
            //{
            //    return -1;
            //}
            //string VisitDate = "to_date('" + DateTime.Parse(dtclinic.Rows[0]["VISIT_DATE"].ToString()).ToString("yyyy-MM-dd") + "','yyyy-mm-dd')";
            //string VisitNo = dtclinic.Rows[0]["VISIT_NO"].ToString();
            if (!GetVisitDateAndVisitNo(ClinicNo, out string VisitDate, out string VisitNo))
            {
                return -1;
            }
            string billIndicator = "0";
            if (OrderClass.Equals("C") && Charges == 0 && !string.IsNullOrEmpty(AppointNo))
            {
                billIndicator = "1";
            }
            string sqlor = "Insert Into OUTP_ORDERS_STANDARD(PATIENT_ID  , CLINIC_NO     , ORDER_NO    ,ORDER_SUB_NO, ";
            sqlor += "ORDERED_BY  ,DOCTOR   ,ORDER_DATE  ,DOCTOR_NO , NURSE  , SIGNATURE_NO , USAGE_DESC  , DIAGNOSIS_DESC ,";
            sqlor += "HIS_UNIT_CODE ,OUTP_SERIAL_NO  , APPOINT_NO , ORDER_CLASS, ORDER_TEXT , ORDER_CODE ,DOSAGE ,DOSAGE_UNITS ,";
            sqlor += "ADMINISTRATION , FREQUENCY ,Skin_Flag,PRESC_PSNO  , AMOUNT   , PERFORMED_BY  ,COSTS   ,CHARGES,";
            sqlor += " CHARGE_INDICATOR,SPLIT_FLAG,ITEM_SPEC,FIRM_ID,REPETITION,PRESC_ATTR,UNITS,ABIDANCE,PERFORM_TIMES,";
            sqlor += " FREQ_DETAIL,BATCH_NO,DOSE_PER_UNIT,SERIAL_NO,";
            sqlor += "  TRADE_PRICE ,BATCH_CODE	,GUID	,ITEM_PRICE  , "; //20220402
            sqlor += "BJCA_CN,BJCA_VALUE,BJCA_TIME ,DECOCTION, COUNT_PER_REPETITION,VISIT_DATE ,VISIT_NO,RECIPETYPE ";
            sqlor += "  ) ";
            sqlor += " Values ('" + PatientId + "','" + ClinicNo + "'," + OrderNo + "," + OrderSubNo + ",'";
            sqlor += OrderedBy + "','" + DoctorName + "'," + OrderDate + ",'" + DoctorNo + "','" + Nurse + "','" + Signatureno + "','" + Usagedesc + "','" + DiagnosisDesc + "','";
            sqlor += HisUnitCode + "','" + OutpSerialNo + "','" + AppointNo + "','" + OrderClass + "','" + OrderText + "','" + OrderCode + "','" + Dosage + "','" + DosageUnits + "','";
            sqlor += Administration + "','" + Frequency + "','" + Skintest + "','" + Prescpsno + "'," + Amount + ",'" + Performedby + "'," + Costs + "," + Charges + ",";
            sqlor += " " + billIndicator + ",'" + SplitFlag + "','" + ItemSpec + "','" + FirmId + "','" + Repetition + "','" + PrescAttr + "',";
            sqlor += "'" + Units + "','" + Abidance + "','" + PerformTimes + "',";
            sqlor += " '" + FreqDetail + "','" + BatchNo + "','" + DosePerUnit + "','" + OutpSerialNo + "',";
            sqlor += "'" + _TRADE_PRICE + "','" + _BATCH_CODE + "','" + _GUID + "','" + _ITEM_PRICE + "', ";  //20220402
            sqlor += " '" + bjca_sn + "','" + bjca_value + "','" + bjca_time + "','" + Decoction + "'," + COUNT_PER_REPETITION + "," + VisitDate + ",'" + VisitNo + "','" + RECIPETYPE + "'";
            sqlor += "  )";
            idc.Add(sqlor, "门诊医嘱新增出错1OUTP_ORDERS_STANDARD！" + sqlor);
            return 0;
        }

        public int set_outporderscosts(ref Dictionary<string, string> idc, string PatientId, string ClinicNo, string OrderNo, string OrderSubNo, string ItemNo, string ItemClass, string ItemName, string ItemCode, string ItemSpec, string Units, string Repetition, decimal Amount, string classonrcpt, decimal costs, decimal charges, string rcptNO, string chargeIndicator, string classOnReckoning, string SubjCode, string PriceQuotiety, decimal ItemPrice, string InsuranceFlag, string Clxzbs, string Clwzm, string HisUnitCode, string OutpSerialNo, string OrderClass, string PerformedBy, decimal _TRADE_PRICE, string _BATCH_CODE, string _GUID, string _BATCH_NO, string Ypxzbs = "", string req_dept = "", string req_doctno = "", string RECIPETYPE = "")
        {
            //string sqlc = "select  VISIT_DATE,VISIT_NO from clinic_master where clinic_no = :t1";
            //System.Collections.ArrayList list1 = new System.Collections.ArrayList();
            //List<string> paras1 = new List<string>();
            //paras1.Add("t1");
            //list1.Add(ClinicNo);
            //DataTable dtclinic = new ServerPublic_Dao().GetDataTable_Para(sqlc, paras1, list1).Tables[0];
            //if (dtclinic.Rows.Count <= 0)
            //{
            //    return -1;
            //}
            //string VisitDate = "to_date('" + DateTime.Parse(dtclinic.Rows[0]["VISIT_DATE"].ToString()).ToString("yyyy-MM-dd") + "','yyyy-mm-dd')";
            //string VisitNo = dtclinic.Rows[0]["VISIT_NO"].ToString();
            if (!GetVisitDateAndVisitNo(ClinicNo, out string VisitDate, out string VisitNo))
            {
                return -1;
            }
            if (OrderClass.Equals("C") && charges == 0)
            {
                chargeIndicator = "1";
            }
            string sqlor = "Insert Into OUTP_ORDERS_COSTS_STANDARD(PATIENT_ID  , CLINIC_NO     , ORDER_NO    ,ORDER_SUB_NO, ";
            sqlor += "ITEM_NO , ITEM_CLASS, ITEM_NAME , ITEM_CODE , ITEM_SPEC , UNITS, REPETITION , AMOUNT,";
            sqlor += "CLASS_ON_RCPT, COSTS , CHARGES ,RCPT_NO ,CHARGE_INDICATOR ,  CLASS_ON_RECKONING ,SUBJ_CODE ,";
            sqlor += "PRICE_QUOTIETY ,ITEM_PRICE  , INSURANCE_FLAG , ";
            sqlor += "CLXZBS  ,CLWZM ,HIS_UNIT_CODE, OUTP_SERIAL_NO,ORDER_CLASS,PERFORMED_BY, SERIAL_NO, ";
            sqlor += "  VISIT_DATE , VISIT_NO,YPXZBS,ORDERED_BY_DEPT,ORDERED_BY_DOCTOR ";
            sqlor += " , TRADE_PRICE	,BATCH_CODE	,BATCH_NO	,GUID ,RECIPETYPE"; //20220402 
            sqlor += " )";
            sqlor += " Values ('" + PatientId + "','" + ClinicNo + "'," + OrderNo + "," + OrderSubNo + ",'";
            sqlor += ItemNo + "','" + ItemClass + "','" + ItemName + "','" + ItemCode + "','" + ItemSpec + "','" + Units + "','" + Repetition + "'," + Amount + ",'";
            sqlor += classonrcpt + "','" + costs + "','" + charges + "','" + rcptNO + "','" + chargeIndicator + "','" + classOnReckoning + "','" + SubjCode + "','";
            sqlor += PriceQuotiety + "'," + ItemPrice + ",'" + InsuranceFlag + "','";
            sqlor += Clxzbs + "','" + Clwzm + "','" + HisUnitCode + "','" + OutpSerialNo + "','" + OrderClass + "','" + PerformedBy + "','" + OutpSerialNo + "',";
            sqlor += VisitDate + ",'" + VisitNo + "'" + ",'" + Ypxzbs + "'";
            sqlor += ",'" + req_dept + "'" + ",'" + req_doctno + "'";
            sqlor += ",'" + _TRADE_PRICE + "','" + _BATCH_CODE + "','" + _BATCH_NO + "','" + _GUID + "','" + RECIPETYPE + "'";
            sqlor += " )";
            idc.Add(sqlor, "门诊医嘱计价项目新增出错1B！" + sqlor);

            string sqlor1 = "Insert Into IND_OUTP_ORDERS_COSTS(PATIENT_ID  , CLINIC_NO     , ORDER_NO    ,ORDER_SUB_NO, ";
            sqlor1 += "ITEM_NO , ITEM_CLASS, ITEM_NAME , ITEM_CODE , ITEM_SPEC , UNITS, REPETITION , AMOUNT,";
            sqlor1 += "CLASS_ON_RCPT, COSTS , CHARGES ,RCPT_NO ,CHARGE_INDICATOR ,  CLASS_ON_RECKONING ,SUBJ_CODE ,";
            sqlor1 += "PRICE_QUOTIETY ,ITEM_PRICE  , INSURANCE_FLAG , ";
            sqlor1 += "CLXZBS  ,CLWZM ,HIS_UNIT_CODE, OUTP_SERIAL_NO,ORDER_CLASS,VISIT_DATE,VISIT_NO ,PERFORMED_BY,YPXZBS ,";
            sqlor1 += " TRADE_PRICE,BATCH_CODE,GUID,BATCH_NO,RECIPETYPE    ) ";
            sqlor1 += " Values ('" + PatientId + "','" + ClinicNo + "'," + OrderNo + "," + OrderSubNo + ",'";
            sqlor1 += ItemNo + "','" + ItemClass + "','" + ItemName + "','" + ItemCode + "','" + ItemSpec + "','" + Units + "','" + Repetition + "'," + Amount + ",'";
            sqlor1 += classonrcpt + "','" + costs + "','" + charges + "','" + rcptNO + "','" + chargeIndicator + "','" + classOnReckoning + "','" + SubjCode + "','";
            sqlor1 += PriceQuotiety + "'," + ItemPrice + ",'" + InsuranceFlag + "','";
            sqlor1 += Clxzbs + "','" + Clwzm + "','" + HisUnitCode + "','" + OutpSerialNo + "','" + OrderClass + "'," + VisitDate + ",'" + VisitNo + "','" + PerformedBy + "','" + Ypxzbs + "',";
            sqlor1 += "'" + _TRADE_PRICE + "' , '" + _BATCH_CODE + "' , '" + _GUID + "' , '" + _BATCH_NO + "','" + RECIPETYPE + "' )";
            idc.Add(sqlor1, "门诊医嘱计价项目临时表新增出错2！" + sqlor1);



            return 0;
        }

        public int set_outporderscosts_free(ref Dictionary<string, string> idc, string PatientId, string ClinicNo, string OrderNo, string OrderSubNo, string ItemNo, string ItemClass, string ItemName, string ItemCode, string ItemSpec, string Units, string Repetition, decimal Amount, string classonrcpt, decimal costs, decimal charges, string rcptNO, string chargeIndicator, string classOnReckoning, string SubjCode, string PriceQuotiety, decimal ItemPrice, string InsuranceFlag, string Clxzbs, string Clwzm, string HisUnitCode, string OutpSerialNo, string OrderClass, string PerformedBy, decimal _TRADE_PRICE, string _BATCH_CODE, string _BATCH_NO, string _GUID, string Ypxzbs = "", string RECIPETYPE = "")
        {
            //string sqlc = "select  VISIT_DATE,VISIT_NO from clinic_master where clinic_no = :t1";
            //System.Collections.ArrayList list1 = new System.Collections.ArrayList();
            //List<string> paras1 = new List<string>();
            //paras1.Add("t1");
            //list1.Add(ClinicNo);
            //DataTable dtclinic = new ServerPublic_Dao().GetDataTable_Para(sqlc, paras1, list1).Tables[0];
            //if (dtclinic.Rows.Count <= 0)
            //{
            //    return -1;
            //}

            //string VisitDate = "to_date('" + DateTime.Parse(dtclinic.Rows[0]["VISIT_DATE"].ToString()).ToString("yyyy-MM-dd") + "','yyyy-mm-dd')";
            //string VisitNo = dtclinic.Rows[0]["VISIT_NO"].ToString();
            if (!GetVisitDateAndVisitNo(ClinicNo, out string VisitDate, out string VisitNo))
            {
                return -1;
            }
            string sqlor = "Insert Into OUTP_ORDERS_COSTS_STANDARD(PATIENT_ID  , CLINIC_NO     , ORDER_NO    ,ORDER_SUB_NO, ";
            sqlor += "ITEM_NO , ITEM_CLASS, ITEM_NAME , ITEM_CODE , ITEM_SPEC , UNITS, REPETITION , AMOUNT,";
            sqlor += "CLASS_ON_RCPT, COSTS , CHARGES ,RCPT_NO ,CHARGE_INDICATOR ,  CLASS_ON_RECKONING ,SUBJ_CODE ,";
            sqlor += "PRICE_QUOTIETY ,ITEM_PRICE  , INSURANCE_FLAG , ";
            sqlor += "CLXZBS  ,CLWZM ,HIS_UNIT_CODE, OUTP_SERIAL_NO,ORDER_CLASS,PERFORMED_BY , SERIAL_NO , ";
            sqlor += " VISIT_DATE , VISIT_NO,YPXZBS ";
            sqlor += ", TRADE_PRICE   ,BATCH_CODE ,BATCH_NO ,GUID,RECIPETYPE ";
            sqlor += "  )";
            sqlor += " Values ('" + PatientId + "','" + ClinicNo + "'," + OrderNo + "," + OrderSubNo + ",'";
            sqlor += ItemNo + "','" + ItemClass + "','" + ItemName + "','" + ItemCode + "','" + ItemSpec + "','" + Units + "','" + Repetition + "'," + Amount + ",'";
            sqlor += classonrcpt + "','" + costs + "','" + charges + "','" + rcptNO + "','" + chargeIndicator + "','" + classOnReckoning + "','" + SubjCode + "','";
            sqlor += PriceQuotiety + "'," + ItemPrice + ",'" + InsuranceFlag + "','";
            sqlor += Clxzbs + "','" + Clwzm + "','" + HisUnitCode + "','" + OutpSerialNo + "','" + OrderClass + "','" + PerformedBy + "','" + OutpSerialNo + "',";
            sqlor += VisitDate + ",'" + VisitNo + "'" + ",'" + Ypxzbs + "'";
            sqlor += ",'" + _TRADE_PRICE + "','" + _BATCH_CODE + "','" + _BATCH_NO + "','" + _GUID + "','" + RECIPETYPE + "'"; //20220402
            sqlor += " )";
            idc.Add(sqlor, "门诊医嘱计价项目新增出错1A！" + sqlor);
            return 0;
        }

        public int set_outporders_free(ref Dictionary<string, string> idc, string PatientId, string ClinicNo, string OrderNo, string OrderSubNo, string OrderedBy, string DoctorName, string OrderDate, string DoctorNo, string Nurse, string Signatureno, string Usagedesc, string DiagnosisDesc, string HisUnitCode, string OutpSerialNo, string AppointNo, string OrderClass, string OrderText, string OrderCode, string Dosage, string DosageUnits, string Administration, string Frequency, string Skintest, string Prescpsno, decimal Amount, string Performedby, decimal Costs, decimal Charges, string SplitFlag, string ItemSpec, string FirmId, string Repetition, string PrescAttr, string Units, string Abidance, string PerformTimes, string FreqDetail, string BatchNo, string DosePerUnit, string bjca_sn = "", string bjca_value = "", string bjca_time = "", string Decoction = "", string COUNT_PER_REPETITION = "null", string RECIPETYPE = "")
        {
            if (Amount == 0)
            {
                Amount = 1;
            }

            //string sqlc = "select  VISIT_DATE,VISIT_NO from clinic_master where clinic_no = :t1";
            //System.Collections.ArrayList list1 = new System.Collections.ArrayList();
            //List<string> paras1 = new List<string>();
            //paras1.Add("t1");
            //list1.Add(ClinicNo);
            //DataTable dtclinic = new ServerPublic_Dao().GetDataTable_Para(sqlc, paras1, list1).Tables[0];
            //if (dtclinic.Rows.Count <= 0)
            //{
            //    return -1;
            //}
            //string VisitDate = "to_date('" + DateTime.Parse(dtclinic.Rows[0]["VISIT_DATE"].ToString()).ToString("yyyy-MM-dd") + "','yyyy-mm-dd')";
            //string VisitNo = dtclinic.Rows[0]["VISIT_NO"].ToString();
            if (!GetVisitDateAndVisitNo(ClinicNo, out string VisitDate, out string VisitNo))
            {
                return -1;
            }
            string sqlor = "Insert Into OUTP_ORDERS_STANDARD ( ";
            sqlor += " PATIENT_ID  , CLINIC_NO     , ORDER_NO    ,ORDER_SUB_NO, ";
            sqlor += " ORDERED_BY  ,DOCTOR   ,ORDER_DATE  ,DOCTOR_NO , NURSE  , ";
            sqlor += " SIGNATURE_NO , USAGE_DESC  , DIAGNOSIS_DESC ,";
            sqlor += " HIS_UNIT_CODE ,OUTP_SERIAL_NO  , APPOINT_NO , ORDER_CLASS, ";
            sqlor += " ORDER_TEXT , ORDER_CODE ,DOSAGE ,DOSAGE_UNITS ,";
            sqlor += " ADMINISTRATION , FREQUENCY ,Skin_Flag,PRESC_PSNO  , ";
            sqlor += "  AMOUNT   , PERFORMED_BY  ,COSTS   ,CHARGES,CHARGE_INDICATOR, ";
            sqlor += "  SPLIT_FLAG,ITEM_SPEC,FIRM_ID,REPETITION,PRESC_ATTR, ";
            sqlor += " UNITS,ABIDANCE,PERFORM_TIMES,FREQ_DETAIL,BATCH_NO,DOSE_PER_UNIT, ";
            sqlor += "  BJCA_CN,BJCA_VALUE,BJCA_TIME ,DECOCTION,COUNT_PER_REPETITION ,VISIT_DATE ,VISIT_NO  ,RECIPETYPE ";
            sqlor += "  )  Values ( ";
            sqlor += "'" + PatientId + "','" + ClinicNo + "'," + OrderNo + "," + OrderSubNo + ",'";
            sqlor += OrderedBy + "','" + DoctorName + "'," + OrderDate + ",'" + DoctorNo + "','" + Nurse + "','" + Signatureno + "','" + Usagedesc + "','" + DiagnosisDesc + "','";
            sqlor += HisUnitCode + "','" + OutpSerialNo + "','" + AppointNo + "','" + OrderClass + "','" + OrderText + "','" + OrderCode + "','" + Dosage + "','" + DosageUnits + "','";
            sqlor += Administration + "','" + Frequency + "','" + Skintest + "','" + Prescpsno + "'," + Amount + ",'" + Performedby + "'," + Costs + "," + Charges + ",1,'" + SplitFlag + "','" + ItemSpec + "','" + FirmId + "','" + Repetition + "','" + PrescAttr + "',";
            sqlor += "'" + Units + "','" + Abidance + "','" + PerformTimes + "','" + FreqDetail + "','" + BatchNo + "','" + DosePerUnit + "','" + bjca_sn + "','" + bjca_value + "','" + bjca_time + "','";
            sqlor += Decoction + "'," + COUNT_PER_REPETITION + ",";
            sqlor += VisitDate + ",'" + VisitNo + "','" + RECIPETYPE + "'";
            sqlor += "  )";
            idc.Add(sqlor, "门诊医嘱新增出错0OUTP_ORDERS_STANDARD！" + sqlor);
            return 0;
        }

        public int set_outporders_free(ref Dictionary<string, string> idc, string PatientId, string ClinicNo, string OrderNo, string OrderSubNo, string OrderedBy, string DoctorName, string OrderDate, string DoctorNo, string Nurse, string Signatureno, string Usagedesc, string DiagnosisDesc, string HisUnitCode, string OutpSerialNo, string AppointNo, string OrderClass, string OrderText, string OrderCode, string Dosage, string DosageUnits, string Administration, string Frequency, string Skintest, string Prescpsno, decimal Amount, string Performedby, decimal Costs, decimal Charges, string SplitFlag, string ItemSpec, string FirmId, string Repetition, string PrescAttr, string Units, string Abidance, string PerformTimes, string FreqDetail, string BatchNo, string DosePerUnit, int ItemNo, string bjca_sn = "", string bjca_value = "", string bjca_time = "", string Decoction = "", string COUNT_PER_REPETITION = "null", string RECIPETYPE = "")
        {
            throw new NotImplementedException();
        }

        public int set_outporders_treat(ref Dictionary<string, string> idc, string PatientId, string ClinicNo, string OrderNo, string OrderSubNo, string OrderedBy, string DoctorName, string OrderDate, string DoctorNo, string Nurse, string Signatureno, string Usagedesc, string DiagnosisDesc, string HisUnitCode, string OutpSerialNo, string AppointNo, string OrderClass, string OrderText, string OrderCode, string Dosage, string DosageUnits, string Administration, string Frequency, string Skintest, string Prescpsno, decimal Amount, string Performedby, decimal Costs, decimal Charges, string SplitFlag, string ItemSpec, string FirmId, string Repetition, string PrescAttr, string Units, string Abidance, string PerformTimes, string FreqDetail, string BatchNo, string DosePerUnit, decimal _TRADE_PRICE, string _BATCH_CODE, string _GUID, decimal _ITEM_PRICE, int itemNo, string bjca_sn = "", string bjca_value = "", string bjca_time = "", string Decoction = "", string TreatItem = "0")
        {
            if (Amount == 0)
            {
                Amount = 1;
            }

            //string sqlc = "select  VISIT_DATE,VISIT_NO from clinic_master where clinic_no = :t1";
            //System.Collections.ArrayList list1 = new System.Collections.ArrayList();
            //List<string> paras1 = new List<string>();
            //paras1.Add("t1");
            //list1.Add(ClinicNo);
            //DataTable dtclinic = new ServerPublic_Dao().GetDataTable_Para(sqlc, paras1, list1).Tables[0];
            //if (dtclinic.Rows.Count <= 0)
            //{
            //    return -1;
            //}
            //string VisitDate = "to_date('" + DateTime.Parse(dtclinic.Rows[0]["VISIT_DATE"].ToString()).ToString("yyyy-MM-dd") + "','yyyy-mm-dd')";
            //string VisitNo = dtclinic.Rows[0]["VISIT_NO"].ToString();
            if(!GetVisitDateAndVisitNo(ClinicNo,out string VisitDate,out string VisitNo))
            {
                return -1;
            }
            string billIndicator = "0";
            if (OrderClass.Equals("C") && Charges == 0 && !string.IsNullOrEmpty(AppointNo))
            {
                billIndicator = "1";
            }
            string sqlor = "Insert Into OUTP_ORDERS_STANDARD(PATIENT_ID  , CLINIC_NO     , ORDER_NO    ,ORDER_SUB_NO, ";
            sqlor += "ORDERED_BY  ,DOCTOR   ,ORDER_DATE  ,DOCTOR_NO , NURSE  , SIGNATURE_NO , USAGE_DESC  , DIAGNOSIS_DESC ,";
            sqlor += "HIS_UNIT_CODE ,OUTP_SERIAL_NO  , APPOINT_NO , ORDER_CLASS, ORDER_TEXT , ORDER_CODE ,DOSAGE ,DOSAGE_UNITS ,";
            sqlor += "ADMINISTRATION , FREQUENCY ,Skin_Flag,PRESC_PSNO  , AMOUNT   , PERFORMED_BY  ,COSTS   ,CHARGES,CHARGE_INDICATOR,SPLIT_FLAG,ITEM_SPEC,FIRM_ID,REPETITION,PRESC_ATTR,UNITS,ABIDANCE,PERFORM_TIMES,FREQ_DETAIL,BATCH_NO,DOSE_PER_UNIT,SERIAL_NO,";
            sqlor += "BJCA_CN,BJCA_VALUE,BJCA_TIME ,DECOCTION,VISIT_DATE ,VISIT_NO,TREAT_ITEM ";
            sqlor += "  ,TRADE_PRICE ,BATCH_CODE	,GUID	,ITEM_PRICE "; //20220402
            sqlor += "  ) ";
            sqlor += " Values ('" + PatientId + "','" + ClinicNo + "'," + OrderNo + "," + OrderSubNo + ",'";
            sqlor += OrderedBy + "','" + DoctorName + "'," + OrderDate + ",'" + DoctorNo + "','" + Nurse + "','" + Signatureno + "','" + Usagedesc + "','" + DiagnosisDesc + "','";
            sqlor += HisUnitCode + "','" + OutpSerialNo + "','" + AppointNo + "','" + OrderClass + "','" + OrderText + "','" + OrderCode + "','" + Dosage + "','" + DosageUnits + "','";
            sqlor += Administration + "','" + Frequency + "','" + Skintest + "','" + Prescpsno + "'," + Amount + ",'" + Performedby + "'," + Costs + "," + Charges + "," + billIndicator + ",'" + SplitFlag + "','" + ItemSpec + "','" + FirmId + "','" + Repetition + "','" + PrescAttr + "',";
            sqlor += "'" + Units + "','" + Abidance + "','" + PerformTimes + "','" + FreqDetail + "','" + BatchNo + "','" + DosePerUnit + "','" + OutpSerialNo + "','" + bjca_sn + "','" + bjca_value + "','" + bjca_time + "','";
            sqlor += Decoction + "',";
            sqlor += VisitDate + ",'" + VisitNo + "','" + TreatItem + "'";
            sqlor += ",'" + _TRADE_PRICE + "','" + _BATCH_CODE + "','" + _GUID + "','" + _ITEM_PRICE + "'";
            sqlor += "  )";
            idc.Add(sqlor, "门诊医嘱新增出错1OUTP_ORDERS_STANDARD！" + sqlor);
            return 0;
        }

        public string GetOutpSerialNo()
        {
            return "select OUTP_SERIAL_NO  from OUTP_ORDERS_STANDARD where   his_unit_code =:t1 and  CLINIC_NO = :t2 and APPOINT_NO = :t3 and rownum = 1";
        }

        public string GetSumChargeIndicatorOrderCosts(string HisUnitCode, string ClinicNo, string OrderNo, string OrderSubNo)
        {
            return "select nvl(sum(CHARGE_INDICATOR),0) from OUTPDOCT.OUTP_ORDERS_COSTS_STANDARD where his_unit_code = '" + HisUnitCode + "'and  CLINIC_NO = '" + ClinicNo + "' and ORDER_NO = " + OrderNo + " and ORDER_SUB_NO = " + OrderSubNo;
        }
    }
}
