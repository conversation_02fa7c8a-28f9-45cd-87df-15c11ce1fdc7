using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Repository;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Linq;
using System.Windows.Forms;
using DevExpress.Utils.Extensions;
using DevExpress.XtraBars;
using Tjhis.MedCard.Station.View.OutpCharge.Entities;
using Tjhis.Interface.Station;
using Tjhis.MedCard.Station.View.OutpCharge.Input;
using PlatCommon.SysBase;
using PlatCommon.Base02;
using Tjhis.MedCard.Station.Service;
using Tjhis.Interface.Station.HospitalCard;

namespace Tjhis.MedCard.Station.View.OutpCharge
{
    public partial class FrmOutpOrderCostsAdd : ParentForm
    {
        #region Fields
        private srvCostInput sCostInput = new srvCostInput();//数据访问类
        private DateTime _visitDate;
        private string _visitNo;
        private string _clinicNo;
        private string _patientId;
        #endregion
        public FrmOutpOrderCostsAdd()
        {
            InitializeComponent();
        }

        #region Models
        private static NM_Service.NMService.ServerPublicClient spc;
        private DataTable _patClinicInfoTable;
        private DataTable _treatRecTable;
        private DataTable _outpOrdersTable;

        private DataTable _clinicItemTable;
        private DataTable _billItemTable;
        private DataTable _pricesListTable;

        string _userid = PlatCommon.SysBase.SystemParm.LoginUser.ID;
        string _username = PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME;
        string _message = "";

        private PopupContainerEdit _popupContainerEdit;

        string _isoutpOrderSerialNo = "";
        string _isprimaryClinicName = "";
        string _isprimaryItemName = "";
        decimal _totalCosts;
        decimal _totalCharges = 0;

        private List<MyListName> _listFerq;
        private FrmVisitNoSearch _form;

        //public string DeptCode = SystemParm.GetParameterValue("PERFORMED_DEPT", "MEDCARD", "*", "*", SystemParm.HisUnitCode);
        public string Dept_Code = PlatCommon.Base02.Cs02XMLSetting.ReadKey(Common.Constants.xmlFile, "PERFORMED_DEPT", "performed_by", "");


        #endregion

        #region 绑定对象
        public class PatientInfo
        {
            [DisplayName("病人ID")]
            public string PATIENT_ID { get; set; }
            [DisplayName("就诊序号")]
            public string VISIT_NO { get; set; }
            [DisplayName("就诊日期")]
            public DateTime VISIT_DATE { get; set; }
            [DisplayName("号")]
            public string CLINIC_TYPE { get; set; }
            [DisplayName("姓名")]
            public string NAME { get; set; }
            [DisplayName("性别")]
            public string SEX { get; set; }
            [DisplayName("年龄")]
            public string AGE { get; set; }
            [DisplayName("费别")]
            public string CHARGE_TYPE { get; set; }
        }
        public class OutpTreatRec
        {
            [DisplayName("类别")]
            public string ITEM_CLASS { get; set; }
            [DisplayName("项目名称")]
            public string ITEM_NAME { get; set; }
            [DisplayName("数量")]
            public decimal AMOUNT { get; set; }
            [DisplayName("单位")]
            public string UNITS { get; set; }
            [DisplayName("频次")]
            public string FREQUENCY { get; set; }
            [DisplayName("开单科室")]
            public string ORDERED_BY { get; set; }
            [DisplayName("执行科室")]
            public string PERFORMED_BY { get; set; }
            [DisplayName("应收")]
            public decimal COSTS { get; set; }
            [DisplayName("实收")]
            public decimal CHARGES { get; set; }
            [DisplayName("医生说明")]
            public string FREQ_DETAIL { get; set; }
            [DisplayName("开单序号")]
            public string SERIAL_NO { get; set; }
            [DisplayName("诊疗流水号")]
            public string CLINIC_SERIAL_NO { get; set; }
            [DisplayName("收费标志")]
            public decimal CHARGE_INDICATOR { get; set; }
            [DisplayName("项目编号")]
            public string ITEM_CODE { get; set; }
        }
        public class OutpBillItems
        {
            [DisplayName("类别")]
            public string ITEM_CLASS { get; set; }
            [DisplayName("计价项目")]
            public string ITEM_NAME { get; set; }
            [DisplayName("规格")]
            public string ITEM_SPEC { get; set; }
            [DisplayName("数量")]
            public decimal AMOUNT { get; set; }
            [DisplayName("单位")]
            public string UNITS { get; set; }
            [DisplayName("开单科室")]
            public string ORDERED_BY_DEPT { get; set; }
            [DisplayName("执行科室")]
            public string PERFORMED_BY { get; set; }
            [DisplayName("应收")]
            public decimal COSTS { get; set; }
            [DisplayName("实收")]
            public decimal CHARGES { get; set; }
            [DisplayName("诊疗流水号")]
            public string CLINIC_SERIAL_NO { get; set; }
            [DisplayName("费用流水号")]
            public string BILL_SERIAL_NO { get; set; }
            [DisplayName("收费标志")]
            public decimal CHARGE_INDICATOR { get; set; }
            [DisplayName("项目编号")]
            public string ITEM_CODE { get; set; }
        }
        #endregion

        //载入准备
        #region 界面载入
        private void FrmOutpOrderCostsAdd_Load(object sender, EventArgs e)
        {
            spc = new NM_Service.NMService.ServerPublicClient();

            _listFerq = PlatCommon.Base01.Cs01DtExtend.ToList<MyListName>(spc.GetList(" SELECT  a.FREQ_DESC  NAME FROM PERFORM_FREQ_DICT a  ").Tables[0]);
            repositoryItemLookUpEdit6.DataSource = _listFerq;
            repositoryItemLookUpEdit6.DisplayMember = "NAME";
            repositoryItemLookUpEdit6.ValueMember = "NAME";
            var chargeIndicators = new List<MyCodeName>();
            chargeIndicators.AddRange(new[]{new MyCodeName{CODE = "0", NAME = "未收费"},
                new MyCodeName{CODE = "1", NAME = "已收费"},
                new MyCodeName{ CODE = "2", NAME = "已退费"}});
            myCodeNameBindingSource.DataSource = chargeIndicators;
            PlatCommon.Base02.Cs02Controls.SetGridViewEditState(gridViewTreatRec, 40);
            PlatCommon.Base02.Cs02Controls.SetGridViewEditState(gridViewOrders, 40);

            string deptName = spc.ExecuteScalarStr($" select a.dept_name from dept_dict a where  a.dept_code='{Dept_Code}'");
            textEditDept.Text = deptName;
            GetTreatRec(DateTime.Now);

            string  sql = $"  SELECT a.PATIENT_ID, a.VISIT_DATE,   a.VISIT_NO, a.SERIAL_NO, a.ORDER_CLASS, a.ORDER_NO,a.CLINIC_NO, HIS_UNIT_CODE," +
                         $"a.ORDER_SUB_NO, a.ITEM_NO,a.ITEM_CLASS,      a.ITEM_NAME, a.ITEM_CODE,  a.ITEM_SPEC, a.UNITS, " +
                         $"a.REPETITION, a.AMOUNT,  a.ORDERED_BY_DEPT, a.ORDERED_BY_DOCTOR, a.PERFORMED_BY,  a.CLASS_ON_RCPT, a.COSTS,     " +
                         $"a.CHARGES, a.RCPT_NO,   a.CLASS_ON_RECKONING, a.SUBJ_CODE, a.PRICE_QUOTIETY, " +
                         $"a.ITEM_PRICE,  a.BILL_DATE, a.BILL_NO,   a.INSURANCE_FLAG,     a.INSURANCE_CONSTRAINED_LEVEL, a.CHARGE_INDICATOR,  " +
                         $"a.OUTP_SERIAL_NO " +
                         $"FROM OUTP_ORDERS_COSTS_STANDARD a WHERE 0=1";
            _pricesListTable = spc.GetList(sql).Tables[0];
            outpBillItemsBindingSource.DataSource = _pricesListTable;
            _pricesListTable.TableName = "pricesListTable";
            outpBillItemsBindingSource.ResetBindings(false);

            GetClinicInfo(DateTime.Now);
        }


        #endregion


        #region 挂号信息

        //挂号信息
        private void GetClinicInfo(DateTime visitdate, string visitno = "")
        {
            string sql = $"SELECT a.VISIT_DATE, a.VISIT_NO,   a.PATIENT_ID,   a.NAME,   a.IDENTITY,   a.CHARGE_TYPE,  " +
                         $"  a.AGE,    a.SEX,    a.INSURANCE_TYPE,    a.CLINIC_TYPE,   b.DEPT_NAME,   a.VISIT_DEPT,   " +
                         $" a.SERIAL_NO,    a.REGISTERING_DATE,    a.CLINIC_NO,   a.CLINIC_LABEL " +
                         $"FROM CLINIC_MASTER a, DEPT_DICT b WHERE  a.VISIT_DEPT = b.DEPT_CODE(+) " +
                         $"AND a.VISIT_DATE = {PlatCommon.Base01.Cs01Functions.SqlDateStr(visitdate)} AND  a.VISIT_NO ='{visitno}' ";
            _patClinicInfoTable = spc.GetList(sql).Tables[0];
            patientInfoBindingSource.DataSource = _patClinicInfoTable;
            patientInfoBindingSource.ResetBindings(false);

            string sql1 = " SELECT  PATIENT_ID   , CLINIC_NO     , ORDER_NO    ,ORDER_SUB_NO  ,  REPETITION, VISIT_DATE,VISIT_NO," +
             " ORDERED_BY  ,DOCTOR   ,ORDER_DATE  ,DOCTOR_NO , NURSE  , SIGNATURE_NO , USAGE_DESC  , DIAGNOSIS_DESC ,  " +
            " HIS_UNIT_CODE ,SERIAL_NO,OUTP_SERIAL_NO ,RCPT_NO , APPOINT_NO , ORDER_CLASS, ORDER_TEXT , ORDER_CODE ,DOSAGE ,DOSAGE_UNITS , " +
             "  ADMINISTRATION , FREQUENCY ,PRESC_PSNO  , AMOUNT   , PERFORMED_BY  ,COSTS   ,CHARGES    " +
             " FROM OUTPDOCT.OUTP_ORDERS_STANDARD  " +
             " WHERE 0=1";
            _outpOrdersTable = spc.GetList(sql1).Tables[0];
            _outpOrdersTable.TableName = "outpOrdersTable";
        }

        #endregion

        #region 处置项目

        //处置项目
        private void GetTreatRec(DateTime visitdate, string visitno = "", string performedBy = "")
        {
            string sql = $"  SELECT VISIT_DATE,   VISIT_NO,   SERIAL_NO,   ORDER_NO,    ORDER_CLASS,    ORDER_CODE, PATIENT_ID, DOCTOR,HIS_UNIT_CODE,  " +
                         $"ORDER_TEXT,   ITEM_SPEC,    UNITS,   AMOUNT,    FREQUENCY,     PERFORMED_BY,    " +
                         $"COSTS,   CHARGES, CHARGE_INDICATOR,    APPOINT_NO, CLINIC_NO, ORDER_SUB_NO ,    " +
                         $"FREQ_DETAIL,   OUTP_SERIAL_NO,     DOCTOR_NO,      ORDERED_BY  ,  '' as temp " +
                         $"FROM OUTPDOCT.OUTP_ORDERS_STANDARD WHERE      " +
                         $"VISIT_DATE = {PlatCommon.Base01.Cs01Functions.SqlDateStr(visitdate)}  " +
                         $"AND   VISIT_NO = '{visitno}' and    PERFORMED_BY = '{performedBy}'  order by   ORDER_NO";
            _treatRecTable = spc.GetList(sql).Tables[0];
            _treatRecTable.TableName = "treatRecTable";
            outpTreatRecBindingSource.DataSource = _treatRecTable;
            outpTreatRecBindingSource.ResetBindings(false);
            _treatRecTable.ColumnChanging += TreatRecTable_ColumnChanging;
            DataTable repositoryItemTable;
            string sql1 = $"   SELECT CLINIC_ITEM_CLASS_DICT.CLASS_CODE  CODE, CLINIC_ITEM_CLASS_DICT.CLASS_NAME NAME FROM CLINIC_ITEM_CLASS_DICT  ";
            repositoryItemTable = spc.GetList(sql1).Tables[0];
            repositoryItemLookUpEdit1.DataSource = repositoryItemTable;
            repositoryItemLookUpEdit1.DisplayMember = "NAME";
            repositoryItemLookUpEdit1.ValueMember = "CODE";

            DataTable repositoryItemTable1;
            string sql2 = $"   SELECT  DEPT_CODE as CODE ,    DEPT_NAME as NAME   FROM DEPT_DICT  ";
            repositoryItemTable1 = spc.GetList(sql2).Tables[0];
            repositoryItemLookUpEdit4.DataSource = repositoryItemTable1;
            repositoryItemLookUpEdit4.DisplayMember = "NAME";
            repositoryItemLookUpEdit4.ValueMember = "CODE";

        }

        private void TreatRecTable_ColumnChanging(object sender, DataColumnChangeEventArgs e)
        {
            if (e.Column.ColumnName == "AMOUNT")
            {
                _treatRecTable.ColumnChanging -= TreatRecTable_ColumnChanging;
                DataRow targetRow = e.Row;
                decimal amount = PlatCommon.Base01.Cs01Functions.CInt(e.ProposedValue);
                // decimal amount = Cs01Functions.CDecimal(e.ToString());
                if (amount <= 0)
                {
                    amount = 1;
                    targetRow["AMOUNT"] = amount;
                }


                decimal costs = PlatCommon.Base01.Cs01Functions.CDecimal(targetRow["COSTS"]);
                decimal charges = PlatCommon.Base01.Cs01Functions.CDecimal(targetRow["CHARGES"]);
                decimal treatRecAmount = PlatCommon.Base01.Cs01Functions.CDecimal(targetRow["AMOUNT"]);
                if (treatRecAmount <= 0)
                {
                    treatRecAmount = 1;
                }
                string visitno = targetRow["VISIT_NO"].ToString();
                DateTime visitdate = PlatCommon.Base01.Cs01Functions.CDate(targetRow["VISIT_DATE"]);

                _pricesListTable.ColumnChanging -= PricesListTable_ColumnChanging;

                OutpTreatRecGenManyCosts(targetRow, amount);
                _pricesListTable.ColumnChanging += PricesListTable_ColumnChanging;
                outpTreatRecBindingSource.ResetCurrentItem();
                outpBillItemsBindingSource.ResetBindings(false);
                CheckTreatCharge();
                CheckTotalCharge();
                _treatRecTable.ColumnChanging += TreatRecTable_ColumnChanging;
            }
        }

        #endregion

        #region 费用明细

        //费用明细
        private void GetBillItems(DateTime visitdate, string visitno = "", string performedBy = "")

        {
            string clinicserialno = "";
            if (gridViewTreatRec.RowCount >= 1 && gridViewTreatRec.FocusedRowHandle >= 0)
            {
                clinicserialno = gridViewTreatRec.GetFocusedRowCellValue(colSERIAL_NO).ToString();
            }

            string sql = $"  SELECT a.PATIENT_ID, a.VISIT_DATE,   a.VISIT_NO, a.SERIAL_NO, a.ORDER_CLASS, a.ORDER_NO,a.CLINIC_NO,HIS_UNIT_CODE, " +
                         $"a.ORDER_SUB_NO, a.ITEM_NO,a.ITEM_CLASS,      a.ITEM_NAME, a.ITEM_CODE,  a.ITEM_SPEC, a.UNITS, " +
                         $"a.REPETITION, a.AMOUNT,  a.ORDERED_BY_DEPT, a.ORDERED_BY_DOCTOR, a.PERFORMED_BY,  a.CLASS_ON_RCPT, a.COSTS,     " +
                         $"a.CHARGES, a.RCPT_NO,   a.CLASS_ON_RECKONING, a.SUBJ_CODE, a.PRICE_QUOTIETY, " +
                         $"a.ITEM_PRICE,  a.BILL_DATE, a.BILL_NO,   a.INSURANCE_FLAG,     a.INSURANCE_CONSTRAINED_LEVEL, a.CHARGE_INDICATOR,  " +
                         $"a.OUTP_SERIAL_NO " +
                         $"FROM OUTP_ORDERS_COSTS_STANDARD a "+
                         $"where  a.visit_date ={PlatCommon.Base01.Cs01Functions.SqlDateStr(visitdate)} " +
                         $"and    a.visit_no ='{visitno}' " +
                         $"and   a.performed_by =  '{performedBy}'  ";
            _pricesListTable = spc.GetList(sql).Tables[0];
            _pricesListTable.AcceptChanges();

            outpBillItemsBindingSource.DataSource = _pricesListTable;
            outpBillItemsBindingSource.ResetBindings(false);
            _pricesListTable.ColumnChanging += PricesListTable_ColumnChanging;
            DataTable repositoryItemTable;
            string sql1 = $"    SELECT BILL_ITEM_CLASS_DICT.CLASS_CODE  CODE, BILL_ITEM_CLASS_DICT.CLASS_NAME NAME FROM BILL_ITEM_CLASS_DICT   ";
            repositoryItemTable = spc.GetList(sql1).Tables[0];
            repositoryItemLookUpEdit3.DataSource = repositoryItemTable;
            repositoryItemLookUpEdit3.DisplayMember = "NAME";
            repositoryItemLookUpEdit3.ValueMember = "CODE";

            DataTable repositoryItemTable1;
            string sql2 = $"   SELECT  DEPT_CODE as CODE ,    DEPT_NAME as NAME   FROM DEPT_DICT  ";
            repositoryItemTable1 = spc.GetList(sql2).Tables[0];
            repositoryItemLookUpEdit5.DataSource = repositoryItemTable1;
            repositoryItemLookUpEdit5.DisplayMember = "NAME";
            repositoryItemLookUpEdit5.ValueMember = "CODE";
        }

        private void PricesListTable_ColumnChanging(object sender, DataColumnChangeEventArgs e)
        {

            if (e.Column.ColumnName == "AMOUNT")
            {
                _pricesListTable.ColumnChanging -= PricesListTable_ColumnChanging;
                DataRow targetRow = e.Row;
                if (targetRow == null) return;
                decimal amount = PlatCommon.Base01.Cs01Functions.CDecimal(e.ProposedValue);

                if (amount <= 0)
                {
                    amount = 1;
                    targetRow["AMOUNT"] = amount;
                }

                decimal pricesListAmount = PlatCommon.Base01.Cs01Functions.CDecimal(targetRow["AMOUNT"]);
                if (pricesListAmount != 0)
                {
                    targetRow["COSTS"] = PlatCommon.Base01.Cs01Functions.CDecimal(targetRow["COSTS"]) * (amount / pricesListAmount);
                    targetRow["CHARGES"] = PlatCommon.Base01.Cs01Functions.CDecimal(targetRow["CHARGES"]) * (amount / pricesListAmount);
                }

                outpBillItemsBindingSource.ResetCurrentItem();
                outpBillItemsBindingSource.ResetBindings(false);
                _pricesListTable.ColumnChanging += PricesListTable_ColumnChanging;
                CheckTreatCharge();
                CheckTotalCharge();
            }
        }

        #endregion

        #region 数量列 只能输数字

        //数量列 只能输数字
        private void repositoryItemTextEditAmount_KeyPress(object sender, KeyPressEventArgs e)
        {

        }

        private void trectRecTextEditAmount_KeyPress(object sender, KeyPressEventArgs e)
        {

        }

        #endregion

        #region 改变收费项目数量 重新计算价格

        private void repositoryItemTextEditAmount_EditValueChanged(object sender, EventArgs e)
        {

        }

        //改变收费项目数量 重新计算价格
        private void repositoryItemTextEditAmount_EditValueChanging(object sender, DevExpress.XtraEditors.Controls.ChangingEventArgs e)
        {

            //gridViewOrders.Focus();
            //gridViewOrders.FocusedColumn = colAMOUNT1;

        }
        private void repositoryItemTextEditAmount_Leave(object sender, EventArgs e)
        {
            gridViewOrders.CloseEditor();
            gridViewOrders.UpdateCurrentRow();
            outpBillItemsBindingSource.EndEdit();
        }
        #endregion

        #region 诊疗项目数量改变事件

        private void trectRecTextEditAmount_EditValueChanged(object sender, EventArgs e)
        {
            //gridViewTreatRec.CloseEditor();
            //outpTreatRecBindingSource.EndEdit();
        }

        private void trectRecTextEditAmount_Leave(object sender, EventArgs e)
        {
            gridViewTreatRec.CloseEditor();
            gridViewTreatRec.UpdateCurrentRow();
            outpTreatRecBindingSource.EndEdit();
        }
        private void trectRecTextEditAmount_EditValueChanging(object sender, DevExpress.XtraEditors.Controls.ChangingEventArgs e)
        {
            //decimal amount = Cs01Functions.CDecimal(e.NewValue.ToString());
            //// decimal amount = Cs01Functions.CDecimal(e.ToString());
            //if (amount <= 0)
            //{
            //    amount = 1;
            //}
            //DataRow TagetRow = (outpTreatRecBindingSource.Current as DataRowView).Row;
            //decimal costs = Cs01Functions.CDecimal(TagetRow["COSTS"]);
            //decimal charges = Cs01Functions.CDecimal(TagetRow["CHARGES"]);
            //decimal TreatRecAmount = Cs01Functions.CDecimal(TagetRow["AMOUNT"]);

            //string visitno = TagetRow["VISIT_NO"].ToString();
            //DateTime visitdate = Cs01Functions.CDate(TagetRow["VISIT_DATE"]);

            //string serialNo = TagetRow["SERIAL_NO"].ToString();
            //string ItemClass = TagetRow["ITEM_CLASS"].ToString();
            //string ItemNo = TagetRow["ITEM_NO"].ToString();

            //string filter = $"serial_no='{serialNo}' and  order_class='{ItemClass}' and order_no='{ItemNo}' and order_sub_no=1";

            ////TagetRow["AMOUNT"] = amount;
            ////SetTreatRecCurrentrow(TagetRow);
            //for (int i = 0; i < PricesListTable.Select(filter).Length; i++)
            //{
            //    //decimal pricesListAmount = Cs01Functions.CDecimal(PricesListTable.Rows[i]["Amount"]);
            //    //PricesListTable.Rows[i]["COSTS"] = Cs01Functions.CDecimal(PricesListTable.Rows[i]["COSTS"]) * (amount / TreatRecAmount);
            //    //PricesListTable.Rows[i]["CHARGES"] = Cs01Functions.CDecimal(PricesListTable.Rows[i]["CHARGES"]) * (amount / TreatRecAmount);
            //    //PricesListTable.Rows[i]["AMOUNT"] = Cs01Functions.CDecimal(PricesListTable.Rows[i]["AMOUNT"]) * (amount / TreatRecAmount);
            //    decimal pricesListAmount = Cs01Functions.CDecimal(PricesListTable.Rows[i]["Amount"]);
            //    PricesListTable.Select(filter)[i]["COSTS"] = Cs01Functions.CDecimal(PricesListTable.Select(filter)[i]["COSTS"]) * (amount / TreatRecAmount);
            //    PricesListTable.Select(filter)[i]["CHARGES"] = Cs01Functions.CDecimal(PricesListTable.Select(filter)[i]["CHARGES"]) * (amount / TreatRecAmount);
            //    PricesListTable.Select(filter)[i]["AMOUNT"] = Cs01Functions.CDecimal(PricesListTable.Select(filter)[i]["AMOUNT"]) * (amount / TreatRecAmount);
            //}
            //CheckTreatCharge();
            //CheckTotalCharge();
            //gridViewTreatRec.Focus();
            //gridViewTreatRec.FocusedColumn = colAMOUNT;
        }

        #endregion

        #region 清屏
        private void Clear()
        {
            labelTotalCharges.Text = "合计:";
            // GetTreatRec(DateTime.Now);
            string sql1 = $"  SELECT VISIT_DATE,   VISIT_NO,   SERIAL_NO,   ORDER_NO,    ORDER_CLASS,    ORDER_CODE, PATIENT_ID, DOCTOR, HIS_UNIT_CODE, " +
                         $"ORDER_TEXT,   ITEM_SPEC,    UNITS,   AMOUNT,    FREQUENCY,     PERFORMED_BY,    " +
                         $"COSTS,   CHARGES, CHARGE_INDICATOR,    APPOINT_NO, CLINIC_NO, ORDER_SUB_NO ,    " +
                         $"FREQ_DETAIL,   OUTP_SERIAL_NO,     DOCTOR_NO,      ORDERED_BY  ,  '' as temp " +
                         $"FROM OUTPDOCT.OUTP_ORDERS_STANDARD WHERE   0=1";
            _treatRecTable = spc.GetList(sql1).Tables[0];
            outpTreatRecBindingSource.DataSource = _treatRecTable;
            outpTreatRecBindingSource.ResetBindings(false);

            string sql = $"  SELECT a.PATIENT_ID, a.VISIT_DATE,   a.VISIT_NO, a.SERIAL_NO, a.ORDER_CLASS, a.ORDER_NO,a.CLINIC_NO,HIS_UNIT_CODE, " +
                         $"a.ORDER_SUB_NO, a.ITEM_NO,a.ITEM_CLASS,      a.ITEM_NAME, a.ITEM_CODE,  a.ITEM_SPEC, a.UNITS, " +
                         $"a.REPETITION, a.AMOUNT,  a.ORDERED_BY_DEPT, a.ORDERED_BY_DOCTOR, a.PERFORMED_BY,  a.CLASS_ON_RCPT, a.COSTS,     " +
                         $"a.CHARGES, a.RCPT_NO,   a.CLASS_ON_RECKONING, a.SUBJ_CODE, a.PRICE_QUOTIETY, " +
                         $"a.ITEM_PRICE,  a.BILL_DATE, a.BILL_NO,   a.INSURANCE_FLAG,     a.INSURANCE_CONSTRAINED_LEVEL, a.CHARGE_INDICATOR,  " +
                         $"a.OUTP_SERIAL_NO " +
                         $"FROM OUTP_ORDERS_COSTS_STANDARD a WHERE 0=1";
            _pricesListTable = spc.GetList(sql).Tables[0];
            outpBillItemsBindingSource.DataSource = _pricesListTable;
            outpBillItemsBindingSource.ResetBindings(false);
            GetClinicInfo(DateTime.Now);
        }


        #endregion

        //主事件
        #region 患者id Enter事件

        //患者id Enter事件
        private void PATIENT_IDTextEdit_KeyDown(object sender, KeyEventArgs e)
        {
            string patientid = PATIENT_IDTextEdit.Text;
            if (e.KeyCode == Keys.Enter)
            {
                if (string.IsNullOrEmpty(patientid)) return;

                //if (_form == null)
                {
                    _form = new FrmVisitNoSearch();
                }
                _form.patid = patientid;
                string sql = $"select a.visit_no  from clinic_master a where  a.patient_id='{patientid}'  and  a.visit_date>=trunc(sysdate)-25 and a.visit_date<=trunc(sysdate) " +
                             $" AND    a.returned_date is null    ";
                DataTable dt = spc.GetList(sql).Tables[0];
                if (dt.Rows.Count <= 0)
                {
                    XtraMessageBox.Show("无挂号信息", "提示信息");
                    return;
                }
                DialogResult isok = _form.ShowDialog();
                if (isok == DialogResult.OK)
                {
                    _patientId = _form.patid;
                    _visitDate = _form.visitdate;
                    _visitNo = _form.visitno;
                    _clinicNo = _form.clinicno;
                    //初始化一下
                    GetClinicInfo(_visitDate, _visitNo);
                    GetTreatRec(_visitDate, _visitNo, Dept_Code);
                    GetBillItems(_visitDate, _visitNo, Dept_Code);
                    //_form.Hide();
                    _isoutpOrderSerialNo = "";
                }
                else
                {
                    _visitDate = DateTime.MinValue;
                    _visitNo = string.Empty;
                }

                if (_treatRecTable.Rows.Count != 0)
                {
                    CheckTreatCharge();
                    CheckTotalCharge();
                    gridViewTreatRec.Focus();
                }
                else
                {
                    outpBillItemsBindingSource.Filter = null;
                }



                //string sql = $"SELECT a.PATIENT_ID, a.INP_NO,    a.NAME,     a.SEX,    a.DATE_OF_BIRTH,    a.BIRTH_PLACE,     a.NATION,    a.IDENTITY,    a.CHARGE_TYPE,    a.UNIT_IN_CONTRACT , '' age  FROM PAT_MASTER_INDEX a  WHERE a.NAME = '{name}'";
                //PatClinicInfoTable = dbHelper.GetDataTable(sql);
                //dtlControlPatClinicInfo.DataSource = PatClinicInfoTable;
            }
        }


        #endregion

        #region 选择诊疗项目
        //选择诊疗项目
        private void ClassItemTextEdit_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Space || e.KeyCode == Keys.F9)
            {
                DataRow row = this.gridViewTreatRec.GetDataRow(this.gridViewTreatRec.FocusedRowHandle);
                if (row != null)
                {
                    if (row.RowState != DataRowState.Added) // 该行自上次调用AcceptChanges以来是否更改的判断
                    {
                        return;
                    }
                }

                string sql = $" SELECT ITEM_CLASS,ITEM_NAME,ITEM_CODE,STD_INDICATOR,INPUT_CODE,INPUT_CODE_WB FROM clinic_item_name_dict where ITEM_CLASS not in ('A', 'B')  ORDER BY  item_class asc, INPUT_CODE ASC";
                _clinicItemTable = spc.GetList(sql).Tables[0];

                FrmSelectData form = new FrmSelectData();
                if (!form.IsFormSetted)
                {
                    form.FormText = "选择项目";
                    form.ColumnsCaption = " ITEM_CLASS=诊疗类别,ITEM_NAME=诊疗项目,ITEM_CODE=诊疗项目代码,STD_INDICATOR=正名标志,INPUT_CODE=拼音码,INPUT_CODE_WB=五笔码";
                    form.ColumnsWidth = " ITEM_CODE =80, ITEM_NAME =300, NPUT_CODE=100";
                    form.SearchColumns = "代码=ITEM_CODE,名称=ITEM_NAME,拼音码=INPUT_CODE,五笔码=INPUT_CODE_WB";
                    form.SelectTable = _clinicItemTable;
                    // form.IsFormSetted = true;
                }
                form.DialogResult = DialogResult.None;
                form.ShowDialog(this);
                if (form.DialogResult == DialogResult.OK)
                {
                    string isItemName = "";
                    string isItemCode = "";
                    string isItemClass = "";
                    DataRow dr = form.SelectedDataRow;
                    //if (gridViewTreatRec.RowCount < 1 || gridViewTreatRec.FocusedRowHandle < 0) return;
                    //gridViewTreatRec.SetFocusedRowCellValue(gridViewTreatRec.Columns["ITEM_NAME"], dr["ITEM_NAME"]);
                    //gridViewTreatRec.SetFocusedRowCellValue(gridViewTreatRec.Columns["ITEM_CLASS"], dr["ITEM_CLASS"]);
                    DataRow tagetRow = (outpTreatRecBindingSource.Current as DataRowView).Row;
                    //int TreatRecount = TreatRecTable.Rows.Count - 1;
                    //DataRow TagetRow = TreatRecTable.Rows[TreatRecount];
                    isItemName = dr["ITEM_NAME"].ToString();
                    isItemClass = dr["ITEM_CLASS"].ToString();
                    isItemCode = dr["ITEM_CODE"].ToString();

                    tagetRow["ORDER_CODE"] = isItemCode;
                    tagetRow["ORDER_TEXT"] = isItemName;
                    ((TextEdit)sender).EditValue = isItemName;
                    tagetRow["ORDER_CLASS"] = isItemClass;
                    tagetRow["AMOUNT"] = 1;

                    _isprimaryClinicName = isItemName;
                    SetTreatRecCurrentrow(tagetRow);
                    OutpTreatRecGenManyCosts(tagetRow);
                    //SetCostsCurrentrow(TagetRow); 

                    outpTreatRecBindingSource.EndEdit();
                    outpTreatRecBindingSource.ResetCurrentItem();
                    gridViewTreatRec.FocusedColumn = colAMOUNT;
                    gridViewTreatRec.ShowEditorByMouse();
                }
            }
        }
        #endregion

        #region 费用明细添加 space


        //费用明细添加 space
        private void repositoryItemTextEditBillName_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Space || e.KeyCode == Keys.F9)
            {
                DataRow row = this.gridViewOrders.GetDataRow(this.gridViewOrders.FocusedRowHandle);
                if (row != null)
                {
                    if (row.RowState != DataRowState.Added) // 该行自上次调用AcceptChanges以来是否更改的判断
                    {
                        return;
                    }
                }

                string sql = $"  SELECT   A.ITEM_CLASS,A.ITEM_CODE,A.ITEM_NAME,A.ITEM_SPEC,A.UNITS,A.PRICE,A.PREFER_PRICE,A.FOREIGNER_PRICE,A.PERFORMED_BY,B.INPUT_CODE,B.INPUT_CODE_WB  FROM   CURRENT_PRICE_LIST A, PRICE_ITEM_NAME_DICT B  WHERE A.ITEM_CODE = B.ITEM_CODE  AND A.ITEM_CLASS = B.ITEM_CLASS  ORDER BY   B.INPUT_CODE ASC";
                _billItemTable = spc.GetList(sql).Tables[0];

                FrmSelectData form = new FrmSelectData();
                if (!form.IsFormSetted)
                {
                    form.FormText = "选择项目";
                    form.ColumnsCaption = " ITEM_CLASS=类别,ITEM_CODE=项目代码,ITEM_NAME=诊疗项目,UNITS=项目单位,INPUT_CODE=拼音码,INPUT_CODE_WB=五笔码,ITEM_SPEC=规格,PRICE=零售价,PERFORMED_BY=执行科室";
                    form.ColumnsWidth = " ITEM_CODE =80, ITEM_NAME =300, NPUT_CODE=100";
                    form.SearchColumns = "代码=ITEM_CODE,名称=ITEM_NAME,拼音码=INPUT_CODE,五笔码=INPUT_CODE_WB";
                    form.SelectTable = _billItemTable;
                    //form.IsFormSetted = true;
                }
                form.DialogResult = DialogResult.None;
                form.ShowDialog(this);
                if (form.DialogResult == DialogResult.OK)
                {
                    DataRow dr = form.SelectedDataRow;

                    if (outpBillItemsBindingSource.Current != null)
                    {
                        DataRow tagetRow = (outpBillItemsBindingSource.Current as DataRowView)?.Row;
                        //int PricesCount = PricesListTable.Rows.Count - 1;
                        //DataRow TagetRow = PricesListTable.Rows[PricesCount];

                        tagetRow["ITEM_NAME"] = dr["ITEM_NAME"];
                        tagetRow["ITEM_CLASS"] = dr["ITEM_CLASS"];
                        tagetRow["ITEM_CODE"] = dr["ITEM_CODE"];
                        tagetRow["ITEM_SPEC"] = dr["ITEM_SPEC"];
                        tagetRow["UNITS"] = dr["UNITS"];
                        tagetRow["AMOUNT"] = 1;

                        //outpBillItemsBindingSource.EndEdit();
                        outpBillItemsBindingSource.ResetCurrentItem();
                        //?TagetRow["item_price"] = dr["PRICE"];
                        //查询单价,收据类别等其它信息
                        GetPriceInfo(tagetRow);
                    }

                    _isprimaryItemName = dr["ITEM_NAME"].ToString();

                    CheckTreatCharge();
                    CheckTotalCharge();

                    gridViewOrders.FocusedColumn = colAMOUNT;
                    gridViewOrders.ShowEditorByMouse();
                }
            }
        }
        #endregion

        #region 选行 过滤


        //选行 过滤
        private void gridViewTreatRec_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {

        }
        //datatable删除 没有填写ITEM_NAME的行
        private void DeleteRow(DataTable dt)
        {

            DataRow[] foundRow;
            foundRow = dt.Select("ITEM_NAME='' or ITEM_NAME is null", "");
            foreach (DataRow row in foundRow)
            {
                dt.Rows.Remove(row);
            }
            //outpBillItemsBindingSource.ResetCurrentItem();
        }
        #endregion

        //方法
        #region 01计算当前时间

        //01计算当前时间
        public DateTime GetOracleTime()
        {
            DateTime sysdate = spc.GetSysDate();
            return sysdate;
        }

        #endregion

        #region 03wf_03retrieve

        //03wf_03retrieve
        private void Retrieve()
        {
            //GetTreatRec(DateTime.Now);
            //GetBillItems(DateTime.Now);
            //GetClinicInfo(DateTime.Now);
            if (_treatRecTable.Rows.Count > 0)
            {
                DataRow dr = _treatRecTable.Rows[_treatRecTable.Rows.Count - 1];
                SetTreatRecCurrentrow(dr);
            }
        }

        #endregion

        #region 04 设置TreatRecTable当前行

        //04 设置TreatRecTable当前行
        private void SetTreatRecCurrentrow(DataRow dr)
        {
            if (dr == null)
            {
                return;
            }
            _isprimaryClinicName = dr["ORDER_TEXT"].ToString();
            SetFilterOutpOrdersCosts(dr);
        }


        #endregion

        #region 05 设置pricesListTable当前行

        //05 设置pricesListTable当前行
        //private int SetCostsCurrentrow(DataRow dr)
        //{
        //    isprimaryItemName = PricesListTable.Rows[count]["item_name"].ToString();
        //    return 1;
        //}

        #endregion

        #region 根据字段过滤表的内容

        /// <summary>
        /// 根据字段过滤表的内容
        /// </summary>
        /// <param name="data">表数据</param>
        /// <param name="condition">条件</param>
        /// <returns></returns>
        /// 
        public static DataTable GetDataFilter(DataTable data, string condition)
        {
            if (data != null && data.Rows.Count > 0)
            {
                if (condition.Trim() == "")
                {
                    return data;
                }
                else
                {
                    var newdt = data.Clone();
                    DataRow[] dr = data.Select(condition);
                    for (int i = 0; i < dr.Length; i++)
                    {
                        newdt.ImportRow(dr[i]);
                    }
                    return newdt;
                }
            }
            else
            {
                return null;
            }
        }

        #endregion

        #region 06过滤

        //06过滤
        private void SetFilterOutpOrdersCosts(DataRow dr)
        {
            string visitno = dr["VISIT_NO"].ToString();
            DateTime visitdate = PlatCommon.Base01.Cs01Functions.CDate(dr["VISIT_DATE"]);

            string serialNo = dr["SERIAL_NO"].ToString();
            string itemClass = dr["ORDER_CLASS"].ToString();
            string itemNo = dr["ORDER_NO"].ToString();

            string filter = $"serial_no='{serialNo}' and  order_class='{itemClass}' and order_no='{itemNo}' and order_sub_no=1";
            //PricesListTable.Select(filter);

            outpBillItemsBindingSource.Filter = filter;
            outpBillItemsBindingSource.ResetCurrentItem();


            //if (PricesListTable.Rows.Count > 0)
            //{
            //    SetCostsCurrentrow(PricesListTable.Rows.Count-1);
            //}
        }

        #endregion

        #region 07将诊疗项目对应的价表项目插入到费用明细表中

        //07将诊疗项目对应的价表项目插入到费用明细表中
        private int OutpTreatRecGenManyCosts(DataRow outpTreatRecRow, decimal amount = 1)
        {
            //拉价表项目之前 把之前遗留的数据先删除
            DataTable dt = outpBillItemsBindingSource.DataSource as DataTable;
            if (dt.Rows.Count > 0)
            {
                for (int i = dt.Rows.Count - 1; i >= 0; i--)
                {
                    if (dt.Rows[i].RowState == DataRowState.Deleted)
                    {
                        continue;
                    }
                    if ((dt.Rows[i]["order_no"].ToString() == outpTreatRecRow["order_no"].ToString()) && (PlatCommon.Base01.Cs01Functions.CInt(dt.Rows[i]["CHARGE_INDICATOR"]) != 1))
                    {
                        dt.Rows[i].Delete();
                    }
                }
            }

            var clinicItemName = outpTreatRecRow["ORDER_TEXT"].ToString();
            var clinicItemCode = outpTreatRecRow["ORDER_CODE"].ToString();
            var clinicItemClass = outpTreatRecRow["ORDER_CLASS"].ToString();
            var clinicAmount = PlatCommon.Base01.Cs01Functions.CDecimal(outpTreatRecRow["AMOUNT"]);
            var sql = $"SELECT   CHARGE_ITEM_CLASS,CHARGE_ITEM_CODE,CHARGE_ITEM_SPEC,AMOUNT,UNITS " +
                      $"FROM CLINIC_VS_CHARGE WHERE CLINIC_ITEM_CLASS = '{clinicItemClass}' AND CLINIC_ITEM_CODE = '{clinicItemCode}' ";
            var clinicVsChargeTable = spc.GetList(sql).Tables[0];

            for (var i = 0; i < clinicVsChargeTable.Rows.Count; i++)
            {
                var chargeItemClass = clinicVsChargeTable.Rows[i]["CHARGE_ITEM_CLASS"].ToString();
                var chargeItemCode = clinicVsChargeTable.Rows[i]["CHARGE_ITEM_CODE"].ToString();
                var chargeItemSpec = clinicVsChargeTable.Rows[i]["CHARGE_ITEM_SPEC"].ToString();
                var chargeAmount = PlatCommon.Base01.Cs01Functions.CDecimal(clinicVsChargeTable.Rows[i]["AMOUNT"]);
                var chargeUnits = clinicVsChargeTable.Rows[i]["UNITS"].ToString();

                var drCount = OutpOrdersCostsAdd(outpTreatRecRow);

                if (drCount < 0)
                {
                    return -1;
                }
                
                //DataRow dr = PricesListTable.Rows[drCount];
                var dr = (outpBillItemsBindingSource.Current as DataRowView)?.Row;
                if (dr == null) continue;
                dr["ITEM_CLASS"] = chargeItemClass;
                dr["ITEM_CODE"] = chargeItemCode;
                dr["ITEM_SPEC"] = chargeItemSpec;
                dr["UNITS"] = chargeUnits;
                dr["REPETITION"] = 1;
                dr["AMOUNT"] = Math.Round(chargeAmount * amount, 2, MidpointRounding.AwayFromZero);
                outpBillItemsBindingSource.ResetCurrentItem();
                GetPriceInfo(dr);
                outpTreatRecRow["ITEM_SPEC"] = chargeItemSpec;
            }
            if (_pricesListTable.Rows.Count > 0)
            {
                DeleteRow(_pricesListTable);
            }
            CheckTreatCharge();
            CheckTotalCharge();
            return 1;
        }

        #endregion

        #region 08 写入outp_orders_costs表

        //08 写入outp_orders_costs表
        private int OutpOrdersCostsAdd(DataRow outpTreatRecRow)
        {
            string patientid = PATIENT_IDTextEdit.Text;
            string visitno = VISIT_NOTextEdit.Text;
            DateTime visitdate = PlatCommon.Base01.Cs01Functions.CDate(VISIT_DATEDateEdit.Text);

            //DataRow outpTreatRecRow = TreatRecTable.Rows[count];
            DataTable dt = outpBillItemsBindingSource.DataSource as DataTable;
            if (dt != null)
            {
                DataRow dr = dt.NewRow();

                string sysdate = GetOracleTime().ToString("yyMMdd");
                //string CLINIC_NO = sysdate + SeqRcptNo.PadLeft(6, '0');

                //取费用流水号
                string seqBillSerialNo = spc.ExecuteScalarStr("select bill_serial_no.nextval  from dual").ToString();
                if (seqBillSerialNo == "")
                {
                    XtraMessageBox.Show("取bill_serial_no流水号失败", "提示信息");
                    return -1;
                }
                string billSerialNo = sysdate + seqBillSerialNo.PadLeft(8, '0');

                DataRow dataRow = (outpTreatRecBindingSource.Current as DataRowView)?.Row;
                string itemname = dataRow["ORDER_TEXT"].ToString();
                string clinicserialno = dataRow["OUTP_SERIAL_NO"].ToString();
                string orderedby = dataRow["ORDERED_BY"].ToString();
                string performedby = dataRow["PERFORMED_BY"].ToString();

                //注释列在此时新增不需填写
                dr["PATIENT_ID"] = patientid;
                dr["VISIT_DATE"] = visitdate;
                dr["VISIT_NO"] = visitno;
                dr["CLINIC_NO"] = _clinicNo;
                dr["SERIAL_NO"] = outpTreatRecRow["SERIAL_NO"];
                dr["OUTP_SERIAL_NO"] = outpTreatRecRow["OUTP_SERIAL_NO"];
                dr["ORDER_CLASS"] = outpTreatRecRow["ORDER_CLASS"];
                dr["ORDER_NO"] = outpTreatRecRow["ORDER_NO"];
                dr["ORDER_SUB_NO"] = 1;
                dr["HIS_UNIT_CODE"] = SystemParm.HisUnitCode;
                //  dr["ITEM_NO"] = dt.Rows.Count + 1;
                //if (dt.Rows.Count == 0)
                //{
                //    dr["ITEM_NO"] = 1;
                //}
                //else
                //{
                //    dr["ITEM_NO"] = dt.AsEnumerable().Where(c => c.RowState != DataRowState.Deleted && c.Field<string>("SERIAL_NO") == outpTreatRecRow["SERIAL_NO"]?.ToString() && c.Field<Int16>("ORDER_NO") == Convert.ToInt16(outpTreatRecRow["ITEM_NO"]?.ToString())).Max(a => a.Field<Int16>("ITEM_NO")) + 1;
                //}
                DataRow[] row = dt.Select($"VISIT_NO='{visitno}' and SERIAL_NO='{ outpTreatRecRow["SERIAL_NO"]}' " +
                                          $"and ORDER_NO={PlatCommon.Base01.Cs01Functions.CInt(outpTreatRecRow["ORDER_NO"])}");

                if (row.Length == 0)
                {
                    dr["ITEM_NO"] = 1;
                }
                else
                {
                    int max = PlatCommon.Base01.Cs01Functions.CInt(dt.Compute("max(ITEM_NO)", $"VISIT_NO='{visitno}' " +
                                                                            $"and SERIAL_NO='{ outpTreatRecRow["SERIAL_NO"]}' " +
                                                                            $"and ORDER_NO={PlatCommon.Base01.Cs01Functions.CInt(outpTreatRecRow["ORDER_NO"])}"));
                    dr["ITEM_NO"] = max + 1;
                }

                //dr["ITEM_CLASS"] =;
                //dr["ITEM_NAME"] =;
                ////付数
                //dr["REPETITION"] =;
                dr["ORDERED_BY_DEPT"] = orderedby;
                dr["ORDERED_BY_DOCTOR"] = _userid;
                dr["PERFORMED_BY"] = performedby;
                //dr["CLASS_ON_RCPT"] =;
                dr["COSTS"] = 0;
                dr["CHARGES"] = 0;

                //dr["RCPT_NO"] =;
                //dr["BILL_DESC_NO"] =;
                //dr["BILL_ITEM_NO"] =;

                //dr["CLASS_ON_RECKONING"] =;
                //dr["SUBJ_CODE"] =;
                //dr["PRICE_QUOTIETY"] =;
                //dr["ITEM_PRICE"] =;
                //dr["BILL_DATE"] =;
                //dr["BILL_NO"] =;
                //dr["INSURANCE_FLAG"] =;
                //dr["INSURANCE_CONSTRAINED_LEVEL"] =;

                dr["CHARGE_INDICATOR"] = 0;
                dr["OUTP_SERIAL_NO"] = clinicserialno;
                //dr["BILL_SERIAL_NO"] = billSerialNo;
                //dr["CHECK_AMOUNT"] = ;
                //dr["HANDBACK_AMOUNT"] = ;
                //dr["WAITBACK_AMOUNT"] = ;
                dt.Rows.Add(dr);
            }

            outpBillItemsBindingSource.Position = dt.Rows.Count - 1;
            int dtCount = dt.Rows.Count - 1;
            outpBillItemsBindingSource.ResetCurrentItem();
            gridViewOrders.FocusedColumn = colITEM_NAME1;

            return dtCount;
        }

        #endregion

        #region 09计算价格 方法

        //09计算价格 方法
        private int GetPriceInfo(DataRow tagetRow, int amount = 1)
        {
            //查询单价,收据类别等其它信息
            Boolean specialIndicator;
            string chargeItemName;
            decimal price;
            decimal preferPrice;
            decimal foreignerPrice;
            decimal realPrice;
            decimal costs;
            decimal charges;
            decimal chargePrice = 0;
            decimal defaultFactor;
            int returnVal;
            string classOnOutpRcpt;
            string classOnReckoning;
            string subjCode;
            int liNumerator = 0;
            int liDenominator = 0;
            int liPersonal = 0;

            //DataRow TagetRow = PricesListTable.Rows[count];

            string chargeItemClass = tagetRow["ITEM_CLASS"].ToString();
            string chargeItemCode = tagetRow["ITEM_CODE"].ToString();
            string chargeItemSpec = tagetRow["ITEM_SPEC"].ToString();
            string chargeUnits = tagetRow["UNITS"].ToString();
            decimal ldecAmount = PlatCommon.Base01.Cs01Functions.CInt(tagetRow["AMOUNT"]);

            //获取享受优惠价格标志
            string chargeType = _patClinicInfoTable.Rows[0]["CHARGE_TYPE"].ToString();
            int priceType = PlatCommon.Base01.Cs01Functions.CInt(spc.ExecuteScalarStr($"SELECT CHARGE_PRICE_INDICATOR FROM CHARGE_TYPE_DICT " +
                                                                       $"WHERE  CHARGE_TYPE_NAME ='{chargeType}'"));
            //获取收费系数相关参数
            DataTable chargePriceScheduleTable;
            chargePriceScheduleTable = spc.GetList($"SELECT PRICE_COEFF_NUMERATOR, PRICE_COEFF_DENOMINATOR, CHARGE_SPECIAL_INDICATOR   " +
                                                              $"FROM CHARGE_PRICE_SCHEDULE WHERE   CHARGE_TYPE = '{chargeType}'").Tables[0];
            int chargeSpecialIndicator = PlatCommon.Base01.Cs01Functions.CInt(chargePriceScheduleTable.Rows[0]["CHARGE_SPECIAL_INDICATOR"]);
            int chargeNumerator = PlatCommon.Base01.Cs01Functions.CInt(chargePriceScheduleTable.Rows[0]["PRICE_COEFF_NUMERATOR"]);
            int chargeDenominator = PlatCommon.Base01.Cs01Functions.CInt(chargePriceScheduleTable.Rows[0]["PRICE_COEFF_DENOMINATOR"]);
            if (chargePriceScheduleTable.Rows.Count == 0)
            {
                XtraMessageBox.Show($"费别'{chargeType}'在收费系数字典中未定义！", "提示信息");
                specialIndicator = false;
                chargeNumerator = 1;
                chargeDenominator = 1;
                return -1;
            }
            else
            {
                if (chargeSpecialIndicator != 1)
                {
                    specialIndicator = false;
                }
                else
                {
                    specialIndicator = true;
                }
            }
            //查询单价
            string sql = $"select  item_name,price, prefer_price, foreigner_price, class_on_outp_rcpt, class_on_reckoning, subj_code " +
                         $" From current_price_list " +
                         $" Where(item_class ='{chargeItemClass}') And(item_code ='{chargeItemCode}') And(item_spec ='{chargeItemSpec}') " +
                         $"And  (units ='{chargeUnits}') And(sysdate >= start_date) And(sysdate <= stop_date Or stop_date Is Null) ";
            DataTable priceListTable = spc.GetList(sql).Tables[0];
            if (priceListTable.Rows.Count == 0)
            {
                XtraMessageBox.Show($"当前价表没有查询到 \r\n{chargeItemClass}\r\n{chargeItemCode}\r\n{chargeItemSpec}\r\n单价!", "提示信息");
                return -1;
            }
            //价表参数赋值
            chargeItemName = priceListTable.Rows[0]["item_name"].ToString();
            price = PlatCommon.Base01.Cs01Functions.CDecimal(priceListTable.Rows[0]["price"]);
            preferPrice = PlatCommon.Base01.Cs01Functions.CDecimal(priceListTable.Rows[0]["prefer_price"]);
            foreignerPrice = PlatCommon.Base01.Cs01Functions.CDecimal(priceListTable.Rows[0]["foreigner_price"]);

            classOnOutpRcpt = priceListTable.Rows[0]["class_on_outp_rcpt"].ToString();
            classOnReckoning = priceListTable.Rows[0]["class_on_reckoning"].ToString();
            subjCode = priceListTable.Rows[0]["subj_code"].ToString();


            //计算单价
            if (priceType <= 0)
            {
                realPrice = price;
            }
            else if (priceType == 1)
            {
                realPrice = preferPrice;
            }
            else
            {
                realPrice = foreignerPrice;
            }
            tagetRow["ITEM_NAME"] = chargeItemName;
            tagetRow["ITEM_PRICE"] = realPrice;

            //计算计价金额
            costs = Math.Round(realPrice * ldecAmount, 2, MidpointRounding.AwayFromZero);
            tagetRow["COSTS"] = costs * amount;
            //计算应收金额
            //非医保病人，有特殊项目
            if (specialIndicator)
            {
                // ReSharper disable once PossibleLossOfFraction
                defaultFactor = chargeNumerator / chargeDenominator;
                //计算方法  
                returnVal = CalcChargePriceOutp(chargeType, chargeItemClass, chargeItemCode, chargeItemSpec, realPrice, defaultFactor, chargePrice, liNumerator, liDenominator, liPersonal);
                if (returnVal > 0)//特殊项目
                {
                    charges = ldecAmount * chargePrice;
                }
                else//非特殊项目
                {
                    charges = ldecAmount * realPrice * chargeNumerator / chargeDenominator;
                }
            }
            else //医保病人或无特殊项目
            {
                charges = ldecAmount * realPrice * chargeNumerator / chargeDenominator;
            }
            charges = Math.Round(charges, 2, MidpointRounding.AwayFromZero);
            tagetRow["charges"] = charges * amount;
            if (costs <= 0)
            {
                costs = 1;
            }
            tagetRow["PRICE_QUOTIETY"] = Math.Round(charges / costs, 2);
            tagetRow["CLASS_ON_RCPT"] = classOnOutpRcpt;
            tagetRow["CLASS_ON_RECKONING"] = classOnReckoning;
            tagetRow["SUBJ_CODE"] = subjCode;

            outpBillItemsBindingSource.EndEdit();
            outpBillItemsBindingSource.ResetCurrentItem();
            return 1;
        }

        private int CalcChargePriceOutp(string chargeType, string itemClass, string itemCode, string itemSpec, decimal price, decimal defaultFactor, decimal chargePrice, int proportionNumerator, int proportionDenominator, int personalPrice)
        {
            int vProportionNumerator = 0;
            int vProportionDenominator = 0;
            int vFreeLimit = 0;
            int returnVal = 0;
            DataTable chargeSpecialExceptTable;
            DataTable chargeSpecialItemTable;
            try
            {
                string sql = $"SELECT proportion_numerator,proportion_denominator,free_limit  " +
                             $"FROM charge_special_except_dict " +
                             $"WHERE charge_type ='{chargeType}' AND  item_class ='{itemClass}' " +
                             $"AND  (item_code='{itemCode}' OR item_code = '*') AND   (item_spec ='{itemSpec}' OR item_spec = '*')";
                chargeSpecialExceptTable = spc.GetList(sql).Tables[0];
            }
            catch
            {
                XtraMessageBox.Show("函数出错", "提示信息");
                return returnVal = -1;
            }

            //存在排斥项目
            if (chargeSpecialExceptTable.Rows.Count > 0)
            {
                vProportionNumerator = PlatCommon.Base01.Cs01Functions.CInt(chargeSpecialExceptTable.Rows[0]["proportion_numerator"]);
                vProportionDenominator = PlatCommon.Base01.Cs01Functions.CInt(chargeSpecialExceptTable.Rows[0]["proportion_denominator"]);
                vFreeLimit = PlatCommon.Base01.Cs01Functions.CInt(chargeSpecialExceptTable.Rows[0]["free_limit"]);
                returnVal = 2;
                //按比例
                if (vFreeLimit == 0)
                {
                    if (vProportionNumerator != 0 || vProportionDenominator != 0)
                    {
                        chargePrice = (price * vProportionNumerator) / vProportionDenominator;
                    }
                    else
                    {
                        chargePrice = price;
                    }
                }
                else//按限额
                {
                    chargePrice = price - vFreeLimit;
                    if (chargePrice < 0)//价格低于限额
                    {
                        chargePrice = 0;
                    }
                    returnVal = 4;
                }
            }
            else//不存在排斥项目
            {

                try
                {
                    string sql = $"SELECT proportion_numerator,proportion_denominator,free_limit  FROM  charge_special_item_dict WHERE charge_type ='{chargeType}' AND  item_class ='{itemClass}' AND  (item_code='{itemCode}' OR item_code = '*') AND   (item_spec ='{itemSpec}' OR item_spec = '*')";
                    chargeSpecialItemTable = spc.GetList(sql).Tables[0];
                }
                catch
                {
                    XtraMessageBox.Show("函数出错", "提示信息");
                    return returnVal = -1;
                }

                if (chargeSpecialItemTable.Rows.Count > 0)//存在特殊项目
                {
                    vProportionNumerator = PlatCommon.Base01.Cs01Functions.CInt(chargeSpecialItemTable.Rows[0]["proportion_numerator"]);
                    vProportionDenominator = PlatCommon.Base01.Cs01Functions.CInt(chargeSpecialItemTable.Rows[0]["proportion_denominator"]);
                    vFreeLimit = PlatCommon.Base01.Cs01Functions.CInt(chargeSpecialItemTable.Rows[0]["free_limit"]);

                    returnVal = 1;
                    //按比例
                    if (vFreeLimit == 0)
                    {
                        if (vProportionNumerator != 0 || vProportionDenominator != 0)
                        {
                            chargePrice = (price * vProportionNumerator) / vProportionDenominator;
                        }
                        else
                        {
                            chargePrice = price;
                        }
                    }
                    else//按限额
                    {
                        chargePrice = price - vFreeLimit;
                        if (chargePrice < 0)//价格低于限额
                        {
                            chargePrice = 0;
                        }
                        returnVal = 3;
                    }
                }
                else//不存在特殊项目
                {
                    returnVal = 0;
                    chargePrice = price * defaultFactor;
                }

            }
            proportionNumerator = vProportionNumerator;
            proportionDenominator = vProportionDenominator;
            personalPrice = (int)(price - vFreeLimit);

            if (returnVal > 2 && personalPrice < 0)
            {
                personalPrice = 0;
            }
            return returnVal;
        }

        #endregion

        #region 10计算处置金额

        //10计算处置金额
        private void CheckTreatCharge()
        {

            //if (_treatRecTable.Rows.Count == 0)
            //{
            //    return;
            //}
            //DataRow dataRow = treatRecDataRow;
            //if (dataRow == null)
            //{
            if (outpTreatRecBindingSource.Current == null)
            {
                return;
            }
            DataRow dataRow = (outpTreatRecBindingSource.Current as DataRowView)?.Row;

            // 重新统计处置项目金额
            //decimal costs = 0;
            //decimal charges = 0;
            //totalCosts = Cs01Functions.CDecimal(PricesListTable.Compute("sum(costs)", "true"));
            //totalCharges = Cs01Functions.CDecimal(PricesListTable.Compute("sum(charges)", "true"));
            gridViewOrders.UpdateSummary();
            _totalCosts = PlatCommon.Base01.Cs01Functions.CDecimal(colCOSTS1.SummaryItem.SummaryValue);
            _totalCharges = PlatCommon.Base01.Cs01Functions.CDecimal(colCHARGES1.SummaryItem.SummaryValue);

            if (dataRow != null)
            {
                dataRow["costs"] = _totalCosts;
                dataRow["charges"] = _totalCharges;
            }

            outpTreatRecBindingSource.ResetCurrentItem();
        }
        #endregion

        #region 11总计价费用显示

        //11总计价费用显示
        private void CheckTotalCharge()
        {
            decimal totalChargesAll = 0;
            decimal totalChargesFact = 0;
            if (_pricesListTable.Rows.Count != 0 && _pricesListTable != null)
            {
                totalChargesAll = Math.Round(PlatCommon.Base01.Cs01Functions.CDecimal(_pricesListTable.Compute("sum(charges)", "")), 2, MidpointRounding.AwayFromZero);
                totalChargesFact = Math.Round(PlatCommon.Base01.Cs01Functions.CDecimal(_pricesListTable.Compute("sum(charges)", "CHARGE_INDICATOR=0")), 2, MidpointRounding.AwayFromZero);
            }
            //合计已交费处置项目金额合计、总金额合计
            labelTotalCharges.Text = $@"总计：{totalChargesAll}，未收费：{totalChargesFact}";
        }


        #endregion

        #region 已收费诊疗收费项目不可编辑

        private void gridViewTreatRec_CustomRowCellEdit(object sender, DevExpress.XtraGrid.Views.Grid.CustomRowCellEditEventArgs e)
        {
            var dr = gridViewTreatRec.GetDataRow(e.RowHandle);
            if (dr != null)
            {
                if (1 == PlatCommon.Base01.Cs01Functions.CInt(dr["CHARGE_INDICATOR"]))
                {
                    RepositoryItem repositoryItem = (RepositoryItem)e.RepositoryItem.Clone();
                    repositoryItem.AllowFocused = false;
                    repositoryItem.ReadOnly = true;
                    repositoryItem.Enabled = false;
                    e.RepositoryItem = repositoryItem;
                }
            }

        }

        private void gridViewOrders_CustomRowCellEdit(object sender, DevExpress.XtraGrid.Views.Grid.CustomRowCellEditEventArgs e)
        {
            var dr = gridViewOrders.GetDataRow(e.RowHandle);
            if (dr != null)
            {
                if (1 == PlatCommon.Base01.Cs01Functions.CInt(dr["CHARGE_INDICATOR"]))
                {
                    RepositoryItem repositoryItem = (RepositoryItem)e.RepositoryItem.Clone();
                    repositoryItem.AllowFocused = false;
                    repositoryItem.ReadOnly = true;
                    repositoryItem.Enabled = false;
                    e.RepositoryItem = repositoryItem;
                }
            }

        }

        #endregion

        private void btClose_ItemClick(object sender, ItemClickEventArgs e)
        {
            Close();
        }

        private void btAdd_ItemClick(object sender, ItemClickEventArgs e)
        {
            string isClinicSerialNo = "";
            string isClinicNo = "";

            string patientid = PATIENT_IDTextEdit.Text;
            string visitno = VISIT_NOTextEdit.Text;
            DateTime visitdate = PlatCommon.Base01.Cs01Functions.CDate(VISIT_DATEDateEdit.Text);

            if (patientid.Trim() == "" || visitno.Trim() == "" || visitdate == null)
            {
                XtraMessageBox.Show("请输入病人信息,病历号或挂号日期或挂号序号为空", "提示信息");
                return;
            }

            int count = _treatRecTable.Rows.Count;
            string sqlindex = string.Format("select nvl(max(order_no),0)  from OUTP_ORDERS_STANDARD where his_unit_code = '{0}' and clinic_no = '{1}'", SystemParm.HisUnitCode, _clinicNo);
            count += Convert.ToInt32(spc.ExecuteScalarStr(sqlindex).ToString()) ;

            if (gridViewTreatRec.RowCount > 0 || gridViewTreatRec.FocusedRowHandle > 0)
            {
                if (gridViewTreatRec.GetFocusedRowCellValue(colITEM_NAME).ToString() == "")
                {
                    XtraMessageBox.Show("诊疗项目名称不能为空", "提示信息");
                    return;
                }
            }
            string deptName = spc.ExecuteScalarStr($" select a.dept_name from dept_dict a where  a.dept_code='{Dept_Code}'").ToString();
            string sysdate = GetOracleTime().ToString("yyMMdd");
            if (string.IsNullOrEmpty(_isoutpOrderSerialNo))
            {
                //取开单序号
                string seqOrderSerialNo = spc.ExecuteScalarStr("Select outp_order_serial_no.NextVal  From dual").ToString();
                if (seqOrderSerialNo == "")
                {
                    XtraMessageBox.Show("读取开单序号outp_order_serial_no错误", "提示信息");
                    return;
                }
                _isoutpOrderSerialNo = seqOrderSerialNo;
                string sql = " SELECT  PATIENT_ID   , CLINIC_NO     , ORDER_NO    ,ORDER_SUB_NO  ,  REPETITION,VISIT_DATE,VISIT_NO, " +
            " ORDERED_BY  ,DOCTOR   ,ORDER_DATE  ,DOCTOR_NO , NURSE  , SIGNATURE_NO , USAGE_DESC  , DIAGNOSIS_DESC ,  " +
           " HIS_UNIT_CODE ,SERIAL_NO,OUTP_SERIAL_NO ,RCPT_NO , APPOINT_NO , ORDER_CLASS, ORDER_TEXT , ORDER_CODE ,DOSAGE ,DOSAGE_UNITS , " +
            "  ADMINISTRATION , FREQUENCY ,PRESC_PSNO  , AMOUNT   , PERFORMED_BY  ,COSTS   ,CHARGES    " +
            " FROM OUTPDOCT.OUTP_ORDERS_STANDARD  " +
            " WHERE 0=1";
                _outpOrdersTable = spc.GetList(sql).Tables[0];
                _outpOrdersTable.TableName = "outpOrdersTable";
                _outpOrdersTable.Rows.Add(_outpOrdersTable.NewRow());
                _outpOrdersTable.Rows[0]["PATIENT_ID"] = patientid;
                _outpOrdersTable.Rows[0]["VISIT_DATE"] = visitdate;
                _outpOrdersTable.Rows[0]["VISIT_NO"] = visitno;
                _outpOrdersTable.Rows[0]["SERIAL_NO"] = _isoutpOrderSerialNo;
                _outpOrdersTable.Rows[0]["OUTP_SERIAL_NO"] = _isoutpOrderSerialNo;
                _outpOrdersTable.Rows[0]["DOCTOR_NO"] = _username;
                _outpOrdersTable.Rows[0]["DOCTOR"] = PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME;
                _outpOrdersTable.Rows[0]["ORDER_DATE"] = GetOracleTime();
                _outpOrdersTable.Rows[0]["CLINIC_NO"] = _clinicNo;
                _outpOrdersTable.Rows[0]["ORDERED_BY"] = Dept_Code;
                _outpOrdersTable.Rows[0]["HIS_UNIT_CODE"] = SystemParm.HisUnitCode;
            }


            //取诊疗流水号
            string seqClinicSerialNo = spc.ExecuteScalarStr("select clinic_serial_no.nextval  from dual").ToString();
            if (seqClinicSerialNo == "")
            {
                XtraMessageBox.Show("取clinic_serial_no流水号失败", "提示信息");
                return;
            }
            

            //设定dr 为添加处置 数据源新行      

            DataTable dt = outpTreatRecBindingSource.DataSource as DataTable;
            DataRow dr = dt.NewRow();
            //添加处置项目 所需参数
            dr["VISIT_DATE"] = visitdate;
            dr["VISIT_NO"] = visitno;
            dr["PATIENT_ID"] = patientid;
            dr["SERIAL_NO"] = _isoutpOrderSerialNo;
            dr["OUTP_SERIAL_NO"] = _isoutpOrderSerialNo;
            dr["CLINIC_NO"] = _clinicNo;
            dr["HIS_UNIT_CODE"] = SystemParm.HisUnitCode;
            if (count == 0)
            {
                dr["ORDER_NO"] = 1;
            }
            else
            {
                int max =Convert.ToInt32( dt.AsEnumerable().Where(a => a.RowState != DataRowState.Deleted).Max(s => s.Field<decimal?>("ORDER_NO")) ?? 0);

                ////跳过删除状态的行
                //if (_treatRecTable.Rows[count - 1].RowState == DataRowState.Deleted)
                //{
                //    dr["ITEM_NO"] = Cs01Functions.CInt(_treatRecTable.Rows[count - 2]["ITEM_NO"]) + 1;
                //}
                //else
                //{
                //    dr["ITEM_NO"] = Cs01Functions.CInt(_treatRecTable.Rows[count - 1]["ITEM_NO"]) + 1;

                //}
                //dr["ORDER_NO"] = max + 1;
                dr["ORDER_NO"] = count + 1;
            }
            dr["ORDER_SUB_NO"] =1;
            //dr["FREQUENCY"] =;
            dr["PERFORMED_BY"] = Dept_Code;
            dr["COSTS"] = 0;
            dr["CHARGES"] = 0;
            dr["CHARGE_INDICATOR"] = 0;
            //dr["APPOINT_NO"] =;
            //dr["APPOINT_ITEM_NO"] =;
            //dr["FREQ_DETAIL"] = "";
            //dr["CLINIC_SERIAL_NO"] = isClinicSerialNo;

            dr["ORDERED_BY"] = Dept_Code;
            dr["DOCTOR_NO"] = _userid;
            dr["DOCTOR"] = PlatCommon.SysBase.SystemParm.LoginUser.USER_NAME;
            dt.Rows.Add(dr);
            //SetTreatRecCurrentrow(TreatRecTable.Rows.Count - 1);
            outpTreatRecBindingSource.Position = gridViewTreatRec.DataRowCount;
            outpTreatRecBindingSource.ResetCurrentItem();
            gridViewTreatRec.FocusedRowHandle = gridViewTreatRec.DataRowCount - 1;
            gridViewTreatRec.Focus();
            gridViewTreatRec.FocusedColumn = colITEM_NAME;
            // gridViewTreatRec.SetFocusedRowCellValue[""]
            //x(visitdate, visitno, deptCode);
        }

        private void btDel_ItemClick(object sender, ItemClickEventArgs e)
        {
            string patientid = PATIENT_IDTextEdit.Text;
            string visitno = VISIT_NOTextEdit.Text;
            DateTime visitdate = PlatCommon.Base01.Cs01Functions.CDate(VISIT_DATEDateEdit.Text);

            if (patientid.Trim() == "" || visitno.Trim() == "" || visitdate == null)
            {
                XtraMessageBox.Show("请输入病人信息,病历号或挂号日期或挂号序号为空", "提示信息");
                return;
            }

            if (_patClinicInfoTable.Rows.Count == 0)
            {
                return;
            }

            int treatCount = _treatRecTable.Rows.Count;
            if (treatCount == 0) return;
            if (outpTreatRecBindingSource.Current != null)
            {
                DataRow tagetRow = (outpTreatRecBindingSource.Current as DataRowView)?.Row;

                if (tagetRow != null && PlatCommon.Base01.Cs01Functions.CInt(tagetRow["CHARGE_INDICATOR"]) == 1)
                {
                    XtraMessageBox.Show("要删除的处置项目已经收费,不能删除!", "提示信息");
                    return;
                }

                if (tagetRow != null)
                {
                    string appointNo = tagetRow["appoint_no"].ToString();

                    if (!String.IsNullOrEmpty(appointNo))
                    {
                        XtraMessageBox.Show("要删除的处置项目有申请信息,在此不能删除!", "提示信息");
                        return;
                    }
                }

                visitno = tagetRow["VISIT_NO"].ToString();
                visitdate = PlatCommon.Base01.Cs01Functions.CDate(tagetRow["VISIT_DATE"]);

                string serialNo = tagetRow["SERIAL_NO"].ToString();
                string itemClass = tagetRow["ORDER_CLASS"].ToString();
                string itemNo = tagetRow["ORDER_NO"].ToString();

                string filter = $"serial_no='{serialNo}' and  order_class='{itemClass}' and order_no='{itemNo}' and order_sub_no=1";

                DataRow[] foundRow;
                foundRow = _pricesListTable.Select(filter, "");

                foreach (DataRow row in foundRow)
                {
                    if (PlatCommon.Base01.Cs01Functions.CInt(row["CHARGE_INDICATOR"]) == 1)
                    {
                        XtraMessageBox.Show("要删除的处置项目有对应的收费明细已经收费,不能删除!", "提示信息");
                        return;
                    }
                }
                foreach (DataRow row in foundRow)
                {
                    row.Delete();
                }
            }

            if (outpTreatRecBindingSource.Current != null)
            {
                outpTreatRecBindingSource.RemoveCurrent();
            }


            if (_treatRecTable.Rows.Count > 0)
            {
                if (_treatRecTable.Rows[0].RowState != DataRowState.Deleted)
                {
                    DataRow dr = (outpTreatRecBindingSource.Current as DataRowView)?.Row;
                    SetTreatRecCurrentrow(dr);
                }

            }
            //outpTreatRecBindingSource.ResetCurrentItem();
            outpBillItemsBindingSource.ResetCurrentItem();
            CheckTreatCharge();
            CheckTotalCharge();
            //gridViewTreatRec.DeleteSelectedRows();
        }

        private const int WmKeydown = 0x100;
        private void btSave_ItemClick(object sender, ItemClickEventArgs e)
        {
            gridViewTreatRec.CloseEditor();
            gridViewOrders.CloseEditor();
            if (_treatRecTable.Rows.Count > 0)
            {
                DataRow[] foundRow;
                foundRow = _treatRecTable.Select("ORDER_TEXT='' or ORDER_TEXT is null", "");
                foreach (DataRow row in foundRow)
                {
                    _treatRecTable.Rows.Remove(row);
                }
            }
            if (_pricesListTable.Rows.Count > 0)
            {
                DeleteRow(_pricesListTable);
            }
            outpTreatRecBindingSource.EndEdit();
            outpBillItemsBindingSource.EndEdit();
            //保存数据     
            int count = 0, n1, n2, n3 = 0;
            n1 = PlatCommon.Base01.Cs01DtExtend.GetDataTableChangedCount(_treatRecTable);
            n2 = PlatCommon.Base01.Cs01DtExtend.GetDataTableChangedCount(_outpOrdersTable);
            if (_treatRecTable.Rows.Count == 0)
            {
                n2 = 0;
            }
            n3 = PlatCommon.Base01.Cs01DtExtend.GetDataTableChangedCount(_pricesListTable);
            count = n1 + n2 + n3;
            if (count == 0)
            {
                XtraMessageBox.Show("没有修改，不需要保存。", "提示信息");
                return;
            }
            DataSet ds = new DataSet();
            string iduSqlRuleText = " ";
            try
            {

                if (n1 > 0)
                {
                    iduSqlRuleText = $"  SELECT VISIT_DATE,   VISIT_NO,   SERIAL_NO,   ORDER_NO,    ORDER_CLASS,    ORDER_CODE,  PATIENT_ID,DOCTOR,    " +
                         $"ORDER_TEXT,   ITEM_SPEC,    UNITS,   AMOUNT,    FREQUENCY,     PERFORMED_BY, HIS_UNIT_CODE,   " +
                         $"COSTS,   CHARGES, CHARGE_INDICATOR,    APPOINT_NO, CLINIC_NO, ORDER_SUB_NO ,    " +
                         $"FREQ_DETAIL,   OUTP_SERIAL_NO,     DOCTOR_NO,      ORDERED_BY  " +
                         $"FROM OUTPDOCT.OUTP_ORDERS_STANDARD  ";
                    if (!_treatRecTable.ExtendedProperties.Contains("SQL"))
                    {
                        _treatRecTable.ExtendedProperties.Add("SQL", iduSqlRuleText);
                    }
                    else
                    {
                        _treatRecTable.ExtendedProperties["SQL"] = iduSqlRuleText;
                    }
                    ds.Tables.Add(_treatRecTable.Copy());
                }
                //      if (n2 > 0)
                //      {
                //          iduSqlRuleText  = " SELECT  PATIENT_ID   , CLINIC_NO     , ORDER_NO    ,ORDER_SUB_NO  ,  REPETITION,VISIT_DATE,VISIT_NO, " +
                // " ORDERED_BY  ,DOCTOR   ,ORDER_DATE  ,DOCTOR_NO , NURSE  , SIGNATURE_NO , USAGE_DESC  , DIAGNOSIS_DESC ,  " +
                //" HIS_UNIT_CODE ,SERIAL_NO,OUTP_SERIAL_NO ,RCPT_NO , APPOINT_NO , ORDER_CLASS, ORDER_TEXT , ORDER_CODE ,DOSAGE ,DOSAGE_UNITS , " +
                // "  ADMINISTRATION , FREQUENCY ,SKINTEST,PRESC_PSNO  , AMOUNT   , PERFORMED_BY  ,COSTS   ,CHARGES    " +
                // " FROM OUTPDOCT.OUTP_ORDERS_STANDARD  ";
                //          if (!_outpOrdersTable.ExtendedProperties.Contains("SQL"))
                //          {
                //              _outpOrdersTable.ExtendedProperties.Add("SQL", iduSqlRuleText);
                //          }
                //          else
                //          {
                //              _outpOrdersTable.ExtendedProperties["SQL"] = iduSqlRuleText;
                //          }
                //          ds.Tables.Add(_outpOrdersTable.Copy());
                //      }
                if (n3 > 0)
                {
                    iduSqlRuleText = $"  SELECT a.PATIENT_ID, " +
                         $"a.CLINIC_NO,  " +
                         $"a.HIS_UNIT_CODE,  " +
                         $"a.VISIT_DATE,  " +
                         $" a.VISIT_NO, " +
                         $"a.SERIAL_NO," +
                         $" a.ORDER_CLASS, " +
                         $"a.ORDER_NO, " +
                         $"a.ORDER_SUB_NO, " +
                         $"a.ITEM_NO,a.ITEM_CLASS," +
                         $"a.ITEM_NAME, a.ITEM_CODE,  " +
                         $"a.ITEM_SPEC, " +
                         $"a.UNITS, " +
                         $"a.REPETITION, " +
                         $"a.AMOUNT,  " +
                         $"a.ORDERED_BY_DEPT, " +
                         $"a.ORDERED_BY_DOCTOR, " +
                         $"a.PERFORMED_BY,  " +
                         $"a.CLASS_ON_RCPT, a.COSTS,     " +
                         $"a.CHARGES, " +
                         $"a.RCPT_NO,  " +
                         $"a.CLASS_ON_RECKONING," +
                         $" a.SUBJ_CODE, a.PRICE_QUOTIETY, " +
                         $"a.ITEM_PRICE,  " +
                         $"a.BILL_DATE, " +
                         $"a.BILL_NO,   " +
                         $"a.INSURANCE_FLAG,     " +
                         $"a.INSURANCE_CONSTRAINED_LEVEL, " +
                         $"a.CHARGE_INDICATOR,  " +
                         $"a.OUTP_SERIAL_NO  " +
                         $"FROM OUTP_ORDERS_COSTS_STANDARD a " ;
                    if (!_pricesListTable.ExtendedProperties.Contains("SQL"))
                    {
                        _pricesListTable.ExtendedProperties.Add("SQL", iduSqlRuleText);
                    }
                    else
                    {
                        _pricesListTable.ExtendedProperties["SQL"] = iduSqlRuleText;
                    }
                    ds.Tables.Add(_pricesListTable.Copy());

                    //增加IND_OUTP_ORDERS_COSTS
                    DataTable dtios = _pricesListTable.Copy();
                    dtios.TableName = "IND_OUTP_ORDERS_COSTS";
                    dtios.Columns.Remove("SERIAL_NO");
                    dtios.Columns.Remove("ORDERED_BY_DEPT");
                    dtios.Columns.Remove("ORDERED_BY_DOCTOR");
                    dtios.Columns.Remove("INSURANCE_CONSTRAINED_LEVEL");
                    iduSqlRuleText = $"  SELECT a.PATIENT_ID, " +
                         $"a.CLINIC_NO,  " +
                         $"a.HIS_UNIT_CODE,  " +
                         $"a.VISIT_DATE,  " +
                         $" a.VISIT_NO, " +
                         $" a.ORDER_CLASS, " +
                         $"a.ORDER_NO, " +
                         $"a.ORDER_SUB_NO, " +
                         $"a.ITEM_NO,a.ITEM_CLASS," +
                         $"a.ITEM_NAME, a.ITEM_CODE,  " +
                         $"a.ITEM_SPEC, " +
                         $"a.UNITS, " +
                         $"a.REPETITION, " +
                         $"a.AMOUNT,  " +
                         $"a.PERFORMED_BY,  " +
                         $"a.CLASS_ON_RCPT, a.COSTS,     " +
                         $"a.CHARGES, " +
                         $"a.RCPT_NO,  " +
                         $"a.CLASS_ON_RECKONING," +
                         $" a.SUBJ_CODE, a.PRICE_QUOTIETY, " +
                         $"a.ITEM_PRICE,  " +
                         $"a.BILL_DATE, " +
                         $"a.BILL_NO,   " +
                         $"a.INSURANCE_FLAG,     " +
                         $"a.CHARGE_INDICATOR,  " +
                         $"a.OUTP_SERIAL_NO  " +
                         $"FROM IND_OUTP_ORDERS_COSTS a ";
                    if (!dtios.ExtendedProperties.Contains("SQL"))
                    {
                        dtios.ExtendedProperties.Add("SQL", iduSqlRuleText);
                    }
                    else
                    {
                        dtios.ExtendedProperties["SQL"] = iduSqlRuleText;
                    }
                    ds.Tables.Add(dtios.Copy());
                }
                spc.SaveDataSet(ds);
                _treatRecTable.AcceptChanges();
                _outpOrdersTable.AcceptChanges();
                _pricesListTable.AcceptChanges();
                XtraMessageBox.Show("保存成功", "提示信息");
                //  PATIENT_IDTextEdit.SendMessage(WM_KEYDOWN, 13,0);
                GetClinicInfo(_visitDate, _visitNo);
                GetTreatRec(_visitDate, _visitNo, Dept_Code);
                GetBillItems(_visitDate, _visitNo, Dept_Code);
            }
            catch (Exception ex)
            {
                _message = string.Format("保存。失败1，原因:{0}", ex.Message);
                XtraMessageBox.Show(_message, "提示信息");
            }
            //Retrieve();
        }
        private const int WM_KEYDOWN = 0x100;
        private void btPayment_ItemClick(object sender, ItemClickEventArgs e)
        {
            string patientid = PATIENT_IDTextEdit.Text;
            string visitno = VISIT_NOTextEdit.Text;
            DateTime visitdate = PlatCommon.Base01.Cs01Functions.CDate(VISIT_DATEDateEdit.Text);

            int count = 0, n1, n2, n3 = 0;
            if (string.IsNullOrEmpty(patientid))
            {
                XtraMessageBox.Show("请先输入患者ID。", "提示信息");
                return;
            }

            //n1 = Cs01DtExtend.GetDataTableChangedCount(_treatRecTable);
            //n2 = Cs01DtExtend.GetDataTableChangedCount(_outpOrdersTable);
            //if (_treatRecTable.Rows.Count == 0)
            //{
            //    n2 = 0;
            //}
            //n3 = Cs01DtExtend.GetDataTableChangedCount(_pricesListTable);
            //count = n1 + n2 + n3;
            //if (count > 0)
            //{
            //    XtraMessageBox.Show("请先保存。", "提示信息");
            //    return;
            //}

            FrmOutpChargeByPrepay chargePayForm = new FrmOutpChargeByPrepay();
            chargePayForm.patientid = patientid;
            chargePayForm.visitno = visitno;
            chargePayForm.visitdate = visitdate;
            //chargePayForm.MdiParent = MdiParent;
            //chargePayForm.Show();
            chargePayForm.ShowDialog();
        }

        private void btClear_ItemClick(object sender, ItemClickEventArgs e)
        {
            Clear();
        }

        private void btAddCost_ItemClick(object sender, ItemClickEventArgs e)
        {
            string patientid = PATIENT_IDTextEdit.Text;
            string visitno = VISIT_NOTextEdit.Text;
            DateTime visitdate = PlatCommon.Base01.Cs01Functions.CDate(VISIT_DATEDateEdit.Text);

            if (patientid.Trim() == "" && visitno.Trim() == "" && visitdate <= new DateTime(1900, 1, 1))
            {
                XtraMessageBox.Show("请输入病人信息,病历号或挂号日期或挂号序号为空", "提示信息");
                return;
            }

            if (gridViewTreatRec.RowCount < 1 || gridViewTreatRec.FocusedRowHandle < 0)
            {
                XtraMessageBox.Show("诊疗项目不能为空", "提示信息");
                return;
            }
            else
            {
                if (gridViewTreatRec.GetFocusedRowCellValue(gridViewTreatRec.Columns["ORDER_TEXT"]).ToString() == "")
                {
                    XtraMessageBox.Show("诊疗项目名称不能为空", "提示信息");
                    return;
                }
            }

            if (gridViewOrders.RowCount > 0 || gridViewOrders.FocusedRowHandle > 0)
            {
                if (gridViewOrders.GetFocusedRowCellValue(gridViewOrders.Columns["ITEM_NAME"]).ToString() == "")
                {
                    XtraMessageBox.Show("费用项目名称不能为空", "提示信息");
                    return;
                }
            }
            //定义选中 处置列 列数据 用于赋值
            if (outpTreatRecBindingSource != null)
            {
                DataRow outpTreatRecRow = (outpTreatRecBindingSource.Current as DataRowView)?.Row;
                //int TreatRecount = TreatRecTable.Rows.Count - 1;
                //DataRow TagetRow = TreatRecTable.Rows[TreatRecount];
                OutpOrdersCostsAdd(outpTreatRecRow);
            }
            gridViewOrders.Focus();
            gridViewOrders.FocusedColumn = colITEM_NAME1;
        }

        private void btDelCosts_ItemClick(object sender, ItemClickEventArgs e)
        {
            //if (outpBillItemsBindingSource.Current == null) return;
            if (outpBillItemsBindingSource.Current != null)
            {
                DataRow dr = (outpBillItemsBindingSource.Current as DataRowView)?.Row;
                if (dr != null && PlatCommon.Base01.Cs01Functions.CInt(dr["CHARGE_INDICATOR"]) == 1)
                {
                    XtraMessageBox.Show("要删除的处置项目有对应的收费明细已经收费,不能删除!", "提示信息");
                    return;
                }
                outpBillItemsBindingSource.RemoveCurrent();

            }


            //DataTable dt = outpBillItemsBindingSource.DataSource as DataTable;
            //dbHelper.DataTableSave(dt, "SELECT a.PATIENT_ID, a.VISIT_DATE,   a.VISIT_NO, a.SERIAL_NO, a.ORDER_CLASS, a.ORDER_NO, a.ORDER_SUB_NO, a.ITEM_NO,a.ITEM_CLASS,      a.ITEM_NAME, a.ITEM_CODE,  a.ITEM_SPEC, a.UNITS, a.REPETITION, a.AMOUNT,  a.ORDERED_BY_DEPT, a.ORDERED_BY_DOCTOR, a.PERFORMED_BY,  a.CLASS_ON_RCPT, a.COSTS,     a.CHARGES, a.RCPT_NO,  a.BILL_DESC_NO, a.BILL_ITEM_NO, a.CLASS_ON_RECKONING, a.SUBJ_CODE, a.PRICE_QUOTIETY, a.ITEM_PRICE,  a.BILL_DATE, a.BILL_NO,   a.INSURANCE_FLAG,     a.INSURANCE_CONSTRAINED_LEVEL, a.CHARGE_INDICATOR,  a.CLINIC_SERIAL_NO,a.BILL_SERIAL_NO,  a.CHECK_AMOUNT, a.HANDBACK_AMOUNT, a.WAITBACK_AMOUNT FROM OUTP_ORDERS_COSTS a");

            //outpBillItemsBindingSource.ResetCurrentItem();
            CheckTreatCharge();
            CheckTotalCharge();
        }

        private void gridViewTreatRec_FocusedRowObjectChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowObjectChangedEventArgs e)
        {
            //if (gridViewTreatRec.RowCount < 1 || gridViewTreatRec.FocusedRowHandle < 0) return;
            DataRow tagetRow = (outpTreatRecBindingSource.Current as DataRowView)?.Row;
            //已收费项目不许增加或删除费用
            //if (tagetRow != null)
            //{
            //    if (1 == PlatCommon.Base01.Cs01Functions.CInt(tagetRow["CHARGE_INDICATOR"]))
            //    {
            //        barLbiAddCost.Enabled = false;
            //        barLbiDelCosts.Enabled = false;
            //    }
            //    else
            //    {
            //        barLbiAddCost.Enabled = true;
            //        barLbiDelCosts.Enabled = true;
            //    }
            //}
            SetTreatRecCurrentrow(tagetRow);
            CheckTreatCharge();
            CheckTotalCharge();
        }

        private void barLbiReadCard_ItemClick(object sender, ItemClickEventArgs e)
        {
            try
            {
                //读M1卡
                string idCardNO = "";
                string identityCardType = SystemParm.GetParameterValue("READ_CARD_NO_TYPE", this.AppCode, "*", "*", SystemParm.HisUnitCode);
                if (ReadHospitalCardBusiness.ReadHospitalCard(identityCardType, ref idCardNO) != 1) return;//读取M1卡
                string ls_card_no = idCardNO;
                if (string.IsNullOrEmpty(ls_card_no))
                {
                    XtraMessageBox.Show("卡号为空", "提示", MessageBoxButtons.OK);
                    return;
                }
                string patientId = ls_card_no;
                if (!string.IsNullOrEmpty(patientId))
                {
                    PATIENT_IDTextEdit.Text = patientId;
                    PATIENT_IDTextEdit.SendMessage(0x100, 13, 0);
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message, "提示信息");
            }
        }

        private void barLbiTemplate_ItemClick(object sender, ItemClickEventArgs e)
        {
            string isClinicSerialNo = "";
            string isClinicNo = "";

            string patientid = PATIENT_IDTextEdit.Text;
            string visitno = VISIT_NOTextEdit.Text;
            DateTime visitdate = PlatCommon.Base01.Cs01Functions.CDate(VISIT_DATEDateEdit.Text);

            if (patientid.Trim() == "" || visitno.Trim() == "" || visitdate == null)
            {
                XtraMessageBox.Show("请输入病人信息,病历号或挂号日期或挂号序号为空", "提示信息");
                return;
            }

            frmBillPatternSelect frmPattern = new frmBillPatternSelect();
            frmPattern.deptCode = Dept_Code;
            if (frmPattern.ShowDialog() == DialogResult.OK)
            {
                string strPatternID = frmPattern.SelectedPatternID;
                DataSet dsBillPatternDetail = sCostInput.GetBillPatternDetailItemDict(strPatternID);

                DataSet ds = sCostInput.GetDoctorNo(_patientId, _visitDate, _visitNo);

                if (ds.Tables[0].Rows.Count <= 0)
                {
                    ds = sCostInput.GetDoctorForUserName(Dept_Code, SystemParm.LoginUser.USER_NAME);
                    if (ds.Tables[0].Rows.Count > 0)
                    {
                        XtraMessageBox.Show("该患者无法获取到开单医生编码，所以现默认开单医生为：" + ds.Tables[0].Rows[0]["USER_NAME"].ToString(), "系统提示");

                    }
                    else
                    {
                        XtraMessageBox.Show("该科室无法获取开单医生，无法进行费用录入。请联系技术人员，解决该问题！", "系统提示");
                        return;
                    }

                }
                if (Cs02DataSetHelper.HasRecord(dsBillPatternDetail))
                {
                    for (int i = 0; i < dsBillPatternDetail.Tables[0].Rows.Count; i++)
                    {
                        Add_Treat_Rec(dsBillPatternDetail.Tables[0].Rows[i]);
                    }


                }

                frmPattern.Close();
            }
        }

        #region 添加诊疗项目套餐

        public bool Add_Treat_Rec(DataRow dr)
        {
            try
            {

                //--A-- 新开outp_orders医嘱记录
                string orderSerialNo = sCostInput.GetOrderSerialNo();//获取新流水号
                DataSet ds = sCostInput.GetDoctorNo(_patientId, _visitDate, _visitNo);

                if (ds.Tables[0].Rows.Count <= 0)
                {
                    ds = sCostInput.GetDoctorForUserName(Dept_Code, SystemParm.LoginUser.USER_NAME);
                    //XtraMessageBox.Show("该患者无法获取到开单医生编码，所以现默认开单医生为：" + ds.Tables[0].Rows[0]["USER_NAME"].ToString(), "系统提示");
                }

                if (ds.Tables[0].Rows.Count <= 0)
                {
                    XtraMessageBox.Show("无法获取开单医生的信息，系统不可开单，请联系技术人员！", "系统提示");
                    return false;
                }

                //--B-- 获取诊疗项目对应的价表记录，以备进行金额计算
                DataRow rowTreat = dr; //获取选择的诊疗项目; //获取选择的诊疗项目; //获取选择的诊疗项目
                                       //1.获取诊疗项目的类别和编码
                string clinic_class = rowTreat["ITEM_CLASS"].ToString();  //诊疗项目类别
                string clinic_code = rowTreat["ITEM_CODE"].ToString();    //编码
                string clinic_name = rowTreat["ITEM_NAME"].ToString();    //诊疗项目名称

                DataSet dsPriceList = sCostInput.GetPriceListByClinic(clinic_class, clinic_code);




                //2.计算价表记录总金额
                float treatCosts = 0F;  //标准总价
                float treatCharge = 0F; //实收总价
                float chargeItemPrice = 0F;//单项费用金额
                float costItemPrice = 0F;//单项标准费用
                string treatSerialNo = sCostInput.GetClinicSerialNo();//获取新的诊疗项目流水号
                string treatUnits = "";//新诊疗项目单位（取关联的第一个费用明细单位）
                int count = _treatRecTable.Rows.Count;
                string sqlindex = string.Format("select nvl(max(order_no),0)  from OUTP_ORDERS_STANDARD where his_unit_code = '{0}' and clinic_no = '{1}'", SystemParm.HisUnitCode, _clinicNo);
                count += Convert.ToInt32(spc.ExecuteScalarStr(sqlindex).ToString());
                if (dsPriceList != null && dsPriceList.Tables.Count > 0 && dsPriceList.Tables[0].Rows.Count > 0)
                {
                    float itemPrice = 0F;
                    float itemAmount = 0F;
                    float treatAmount = 0F;                           //新加诊疗项目套餐数量
                    treatUnits = dsPriceList.Tables[0].Rows[0]["UNITS"].ToString();
                    foreach (DataRow drPrice in dsPriceList.Tables[0].Rows)
                    {
                        //价格计算
                        float price, preferPrice, foreignerPrice;
                        float.TryParse(drPrice["PRICE"].ToString(), out price);//标准价格 float.TryParse(drPrice["PRICE"].ToString(), out itemPrice);
                        float.TryParse(drPrice["PREFER_PRICE"].ToString(), out preferPrice);//优惠价格
                        float.TryParse(drPrice["FOREIGNER_PRICE"].ToString(), out foreignerPrice);//-外宾价

                        itemPrice = price;

                        chargeItemPrice = 0F;
                        costItemPrice = 0F;
                        // float.TryParse(drPrice["PRICE"].ToString(), out itemPrice);
                        float.TryParse(drPrice["AMOUNT"].ToString(), out itemAmount);
                        float.TryParse(dr["AMOUNT"].ToString(), out treatAmount);
                        chargeItemPrice = itemPrice * itemAmount * treatAmount;
                        costItemPrice = price * itemAmount * treatAmount;
                        treatCharge += chargeItemPrice;
                        treatCosts += costItemPrice;

                    }
                }

                DataTable dtOutpOrders = outpTreatRecBindingSource.DataSource as DataTable;
                DataRow drNewOrders = dtOutpOrders.NewRow();
                drNewOrders["PATIENT_ID"] = _patientId;
                if (count == 0)
                {
                    drNewOrders["ORDER_NO"] = 1;
                }
                else
                {
                    int max = Convert.ToInt32(dtOutpOrders.AsEnumerable().Where(a => a.RowState != DataRowState.Deleted).Max(s => s.Field<decimal?>("ORDER_NO")) ?? 0);

                    drNewOrders["ORDER_NO"] = count + 1;
                }
                drNewOrders["ORDER_SUB_NO"] = 1;
                drNewOrders["VISIT_DATE"] = _visitDate;
                drNewOrders["VISIT_NO"] = _visitNo;
                drNewOrders["SERIAL_NO"] = orderSerialNo;
                drNewOrders["ORDER_TEXT"] = rowTreat["ITEM_NAME"].ToString();
                drNewOrders["ORDERED_BY"] = Dept_Code;
                drNewOrders["DOCTOR"] = ds.Tables[0].Rows[0]["USER_NAME"].ToString();
                //drNewOrders["ORDER_DATE"] = DateTime.Now;
                drNewOrders["CLINIC_NO"] = _clinicNo;
                drNewOrders["DOCTOR_NO"] = ds.Tables[0].Rows[0]["DB_USER"].ToString();
                //drNewOrders["ORDERED_NURSE"] = SystemParm.LoginUser.NAME;
                //drNewOrders["ORDERED_NURSE_NO"] = SystemParm.LoginUser.USER_NAME;
                drNewOrders["AMOUNT"] = 1;
                drNewOrders["PERFORMED_BY"] = Dept_Code;
                drNewOrders["COSTS"] = treatCosts;
                drNewOrders["UNITS"] = treatUnits;
                drNewOrders["CHARGES"] = treatCharge;
                drNewOrders["CHARGE_INDICATOR"] = 0;
                //drNewOrders["XUANZE"] = 1;
                drNewOrders["ORDER_CODE"] = clinic_code;
                drNewOrders["OUTP_SERIAL_NO"] = orderSerialNo;
                drNewOrders["ORDER_CLASS"] = clinic_class;
                drNewOrders["HIS_UNIT_CODE"] = SystemParm.HisUnitCode;
                //drNewOrders["REPETITION"] = 1;
                dtOutpOrders.Rows.Add(drNewOrders);
                outpTreatRecBindingSource.Position = gridViewTreatRec.DataRowCount;
                outpTreatRecBindingSource.ResetCurrentItem();
                gridViewTreatRec.FocusedRowHandle = gridViewTreatRec.DataRowCount - 1;
                gridViewTreatRec.Focus();
                gridViewTreatRec.FocusedColumn = colITEM_NAME;

                DataRow outpTreatRecRow = (outpTreatRecBindingSource.Current as DataRowView)?.Row;
                //OutpOrdersCostsAdd(outpTreatRecRow);
                SetTreatRecCurrentrow(outpTreatRecRow);
                OutpTreatRecGenManyCosts(outpTreatRecRow);
                return true;



            }
            catch (Exception ex)
            {
                //Error.ErrProc(ex);
                MessageBox.Show(ex.Message);
                Utility.LogFile.WriteLogAutoError(ex, ex.Message, this.GetType().Name);
                return false;
            }

        }

        #endregion
    }
}